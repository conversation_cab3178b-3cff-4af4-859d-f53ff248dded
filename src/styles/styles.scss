@use "sass:color";
@import "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap";

@import "variables";
@import "commons";
@import "left-menu";

html,
body {
  font-family: "Inter", Arial, Helvetica, sans-serif;
}

body {
  background: #fff !important;
}

.svg-font-color svg > path {
  fill: var(--ifm-font-color-base);
}

.MuiDialog-container {
  h3 {
    font-size: 13px;

    &.mb-0 {
      margin-bottom: 0;
    }
  }

  .button-container {
    .MuiButtonBase-root {
      background: none !important;
    }
  }
  .no-button-bg {
    .MuiButtonBase-root {
      background-color: transparent !important;
      height: inherit !important;
      border-radius: 0px !important;
      font-size: inherit !important;
      line-height: inherit !important;
      padding: inherit !important;
      text-transform: inherit !important;
      color: inherit !important;
      box-shadow: none !important;
    }
  }

  .MuiButtonBase-root:not(.MuiCheckbox-root):not(.MuiRadio-root):not(.chips) {
    // commented because these lines making issues all of the buttons in dialog
    // background-color: var(--orange);
    // height: 36px;
    // border-radius: 4px;
    // font-size: 14px;
    // line-height: 20px;
    // padding: 9px 20px;
    // text-transform: capitalize;
    // color: white;
    // box-shadow: none;
    // background: red !important;

    &.derived-chips {
      margin: 3px 3px 0 0 !important;
      border-radius: 4px;
      background: var(--chip-bg);
      height: 30px;
      position: relative;
      font-size: 14px;
      color: var(--dark-grey);
      font-weight: 500;
    }

    &:hover,
    &:active {
      // commented because these lines making issues all of the buttons in dialog
      // background-color: var(--orange);
      // box-shadow: none;
    }
  }

  // .ruleGroup {
  //   background: var(--card-bg);
  //   border:solid 1px var(--table-border);
  //   .ruleGroup-addRule, .ruleGroup-addGroup, .ruleGroup-lock {
  //     border:solid 1px var(--orange);
  //     background:transparent;
  //     font-size:12px;
  //     border-radius: 4px;
  //     padding:4px 8px;
  //     color:var(--orange);
  //     cursor: pointer;
  //   }
  // }
  .queryBuilder-branches {
    .betweenRules {
      select {
        max-width: 120px;
        border: solid 1px var(--input-box-border);
        padding: 8px 8px 8px 4px;
        background: white;
        border-radius: 4px;

        &:focus {
          box-shadow: none;
          outline: none;
        }
      }
    }

    .rule {
      select {
        max-width: 120px;
        border: solid 1px var(--input-box-border);
        padding: 8px 8px 8px 4px;
        background: white;
        border-radius: 4px;

        &:focus {
          box-shadow: none;
          outline: none;
        }
      }
    }

    button {
      border: solid 1px var(--input-box-border);
      padding: 3px;
      background: white;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 25px;
      cursor: pointer;

      &.rule-cloneRule,
      &.ruleGroup-cloneGroup {
        font-size: 0px;
        color: transparent;
        background: url(../assets/svgs/icon-copy-black.svg) 50% 50% no-repeat;
        padding: 16px 20px;
        border: none;
      }

      &.rule-lock {
        font-size: 0px;
        color: transparent;
        background: url(../assets/svgs/icon-lock-black.svg) 50% 50% no-repeat;
        background-size: 21px auto;
        padding: 16px 20px;
        border: none;
      }

      &[title="Lock group"],
      &[title="Lock rule"] {
        background: url(../assets/svgs/icon-unlock-black1.svg) 50% 50% no-repeat !important;
      }

      &.rule-remove,
      &.ruleGroup-remove {
        font-size: 0px;
        color: transparent;
        background: url(../assets/svgs/icon-remove-black.svg) 50% 50% no-repeat;
        padding: 16px 20px;
        border: none;
      }
    }

    .queryBuilder-dragHandle {
      font-size: 0px;
      color: transparent;
      background: url(../assets/svgs/icon-dots-group-gray.svg) 50% 50% no-repeat;
      padding: 16px 20px;
    }
  }
}

// Styles for when "Use validation" option is selected
.validateQuery {
  .queryBuilder {
    .rqb-background-color {
      background: #146fd0 !important;
    }

    // Invalid groups
    .ruleGroup.queryBuilder-invalid {
      background-color: #66339966; // transluscent rebeccapurple

      // Bold the +Rule button if the group has no child rules or groups
      .ruleGroup-addRule {
        font-weight: bold !important;
      }

      // Message to user about empty groups
      & > .ruleGroup-header::after {
        content: "Empty groups are considered invalid. Avoid them by using addRuleToNewGroups.";
        color: white;
      }
    }

    // Invalid rules
    .rule.queryBuilder-invalid {
      // Purple background for empty text fields
      .rule-value {
        background-color: #66339966; // transluscent rebeccapurple

        &::placeholder {
          color: color.scale(rebeccapurple, $lightness: -30%);
        }
      }
    }
  }
}

// Styles for when "Use validation" option is selected (dark theme variations)
html[data-theme="dark"] {
  .validateQuery {
    .queryBuilder {
      .rule.queryBuilder-invalid {
        .rule-value {
          &::placeholder {
            color: color.scale(rebeccapurple, $lightness: 30%);
          }
        }
      }
    }
  }
}

// Styles for when "Justified layout" option is selected
.justifiedLayout {
  .queryBuilder {
    // Push the clone, lock, and remove buttons to the right edge
    .ruleGroup-addGroup + button.ruleGroup-cloneGroup,
    .ruleGroup-addGroup + button.ruleGroup-lock,
    .ruleGroup-addGroup + button.ruleGroup-remove,
    .rule-operators + button.rule-cloneRule,
    .rule-operators + button.rule-lock,
    .rule-operators + button.rule-remove,
    .rule-value + button.rule-cloneRule,
    .rule-value + button.rule-lock,
    .rule-value + button.rule-remove,
    .control + button.rule-cloneRule,
    .control + button.rule-lock,
    .control + button.rule-remove,
    .chakra-select__wrapper + button.rule-cloneRule,
    .chakra-select__wrapper + button.rule-lock,
    .chakra-select__wrapper + button.rule-remove {
      margin-left: auto;
    }
  }
}

.position-relative {
  position: relative;
}

.p-0 {
  padding: 0px !important;
}
.pb-10 {
  padding-bottom: 10px;
}
.pr-20 {
  padding-right: 20px;
}

.small-screen-navbar {
  .MuiPaper-elevation {
    @media (max-width: 600px) {
      overflow: visible !important;
    }
  }
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-weight: 600;
  margin: 0;
}

.h1 {
  font-size: 2rem;
  //color: var(--double-grey);
  line-height: 1.1;
  margin-bottom: 15px;
}

.h3 {
  font-size: 1.25rem;
  color: var(--light-grey);
  line-height: 1.1;
  margin-bottom: 15px;

  &.dark-grey {
    color: var(--dark-grey);
  }
}

.fileUploadButton {
  width: 100%;

  .MuiButtonBase-root {
    background: var(--dark-grey);
    height: 36px;
    padding: 0px;
    justify-content: flex-start;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    overflow: visible;
    color: #fff;
    width: 100%;
    box-shadow: none;
    position: relative;

    &:focus,
    &:active,
    &:hover,
    &.Mui-focusVisible {
      background: var(--dark-grey);
      box-shadow: none;
    }

    .MuiTouchRipple-root {
      display: none;
    }

    input {
      background: #fff;
      width: calc(100% - 40px);
      height: 36px;
      margin-right: 0px;
      padding-left: 10px;
      font-size: 15px;
      color: #2e384d;
      font-weight: 400;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      border: solid 1px var(--input-box-border) !important;

      & + img,
      & + .img-icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 10px;
        svg {
          display: block;
        }
      }
    }
  }

  &.has-error {
    .MuiButtonBase-root {
      input {
        border-color: #d32f2f !important;
        color: #d32f2f !important;
      }
    }
  }
}

.large-file-upload-button {
  width: 100%;
  display: flex;
  align-items: center;
  column-gap: 8px;

  .file-upload {
    border: solid 1px var(--input-box-border);
    width: 100%;
    position: relative;
    height: 36px;
    border-radius: 5px;
    cursor: pointer;
    overflow: hidden;

    span {
      display: block;
      padding-left: 10px;
      height: 34px;
      word-wrap: break-word;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-right: 41px;
      overflow: hidden;
      line-height: 32px;
    }

    &:before {
      position: absolute;
      content: "";
      top: -1px;
      right: 0px;
      height: 36px;
      width: 40px;
      background: var(--dark-grey) url(../assets/svgs/icon-upload-white.svg) 50%
        50% no-repeat;
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;
    }
  }

  .btn-orange {
    cursor: pointer;
    flex: 1 0 auto;

    &:disabled {
      color: rgba(255, 255, 255, 0.3) !important;
    }

    &.btn-close-white {
      padding: 5px !important;

      svg {
        max-height: inherit;
      }
    }
  }
}

.form-control {
  width: 100%;

  &:after {
    display: none;
  }

  &.read-only {
    .MuiInputBase-input {
      border: none;
      background: transparent;
      padding: 4px 0;
      font-weight: 500;
      height: auto;
      -webkit-text-fill-color: inherit;
    }
  }

  .MuiInputLabel-root {
    top: -5px;
  }

  &:before {
    display: none;
  }

  .MuiInputBase-input {
    background: #fff;
    height: 36px;
    padding: 9px 10px;
    font-size: 15px;
    color: #2e384d;
    font-weight: 400;
    border-radius: 4px;
    box-sizing: border-box;
    border: solid 1px var(--input-box-border);

    &::placeholder {
      color: var(--light-grey);
      opacity: 1;
    }

    &:focus {
      box-shadow: none;
    }
  }

  fieldset {
    display: none;
  }

  &.height114 {
    height: 134px;
  }
}

.form-control-1 {
  background: #fff;
  height: 36px;
  padding: 8px 16px !important;
  font-size: 14px;
  color: #2e384d;
  font-weight: 400;
  border-radius: 4px;
  box-sizing: border-box;
  border: none;
  width: 100%;
  border: solid 1px var(--input-box-border);
  display: block;

  &.read-only {
    opacity: 0.6;
    pointer-events: none;
  }

  &::placeholder {
    font-size: 14px;
    color: var(--light-grey);
    font-family: "Inter", Arial, Helvetica, sans-serif;
  }

  &:focus,
  &:focus-visible {
    outline: none;
    box-shadow: none;
  }

  &.input-sm {
    padding: 6px 26px 6px 16px !important;
    height: 32px;
  }

  &.height114 {
    height: 114px;
  }

  &.height58 {
    height: 78px !important;
  }

  .MuiOutlinedInput-notchedOutline {
    display: none;
  }

  &.alternative-1 {
    .MuiInputBase-input {
      padding-left: 0px;
    }
  }

  &.has-error {
    border-color: #d32f2f !important;

    &::placeholder {
      color: #d32f2f;
    }

    .Mui-error {
      color: #d32f2f;
    }
  }

  &.tolrence-data-box {
    .MuiSelect-select {
      padding-left: 0px;
    }
  }
}

textarea.form-control-1 {
  height: 100px;
  &.max-60 {
    height: 60px;
  }
}

select.form-control-1 {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.7rem center;
  background-size: 1em;

  &:focus,
  &:focus-visible {
    outline: none;
    box-shadow: none;
  }

  &.disabled-dropdown {
    background: none;
  }
}

.format-arrow-pos {
  .MuiSvgIcon-root {
    position: absolute;
    top: 50%;
    right: 0px;
    transform: translateY(-50%);
  }
}

.form-group-row {
  &.MuiFormGroup-row {
    display: flex;

    .MuiFormControl-root {
      flex-grow: 1;
    }
  }

  .form-control-autocomplete {
    width: auto;

    .MuiInputBase-formControl {
      .MuiInputBase-input {
        border-top-left-radius: 0px;
        border-bottom-left-radius: 0px;
        border-left: none;
      }
    }
  }

  .form-control {
    width: auto;

    .MuiInputBase-colorPrimary {
      .MuiInputBase-input {
        border-top-left-radius: 0px;
        border-bottom-left-radius: 0px;
        border-left: none;
      }
    }
  }

  .MuiButton-containedPrimary {
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    position: relative;

    &:hover {
      background: none;
    }

    span {
      position: relative;
      z-index: 1;
    }

    &:before {
      position: absolute;
      top: 0px;
      right: 0px;
      bottom: 0px;
      left: 0px;
      background-color: var(--input-box-border);
      opacity: 0.5;
      content: "";
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      z-index: 0;
    }
  }

  &.disabled-row {
    opacity: 0.5;
  }
}

.merge-definition-wrapper {
  margin-bottom: 12px;

  .merge-def-inner {
    background: var(--white);
    margin-left: 0px;
    margin-right: 0px;
    padding: 0px 12px;
    margin-bottom: 0px;
    border: solid 1px var(--table-border);
  }

  h4 {
    margin: 0 0 10px;
  }

  .data-grid {
    margin-left: -12px;
    margin-right: -12px;
    padding-left: 12px;
    padding-right: 12px;
    padding-bottom: 12px;
    padding-top: 12px;

    width: calc(100% + 48px);
    max-width: inherit;
    flex-basis: auto;

    & + .data-grid {
      margin-top: 0px;
      border-top: solid 1px var(--table-border);
    }
  }
}

.form-control-autocomplete {
  width: 100%;
  &.line-height18 {
    .MuiInputLabel-root {
      line-height: 18px;
    }
  }

  &.has-error {
    .MuiInputBase-formControl {
      .MuiInputBase-input {
        border-color: #d32f2f !important;

        &::placeholder {
          color: #d32f2f;
        }
      }
    }
  }

  &.disabled.disable-arrow {
    .MuiButtonBase-root {
      &:before {
        display: none;
      }
    }
  }

  &.search-box {
    .MuiInputBase-input {
      border-right: none;
      border-top-right-radius: 0px;
      border-bottom-right-radius: 0px;
      width: calc(100% - 37px);
      background: var(--white);
      height: 36px;
      padding: 9px 10px !important;
      font-size: 14px;
      color: #2e384d;
      font-weight: 400;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      box-sizing: border-box;
      border: solid 1px var(--input-box-border);
      border-right: none;

      &:focus {
        box-shadow: none;
      }
    }

    .MuiInputAdornment-root {
      margin-left: 0px;
      position: relative;

      &:before {
        content: "";
        left: -1px;
        top: 50%;
        height: 24px;
        position: absolute;
        background: var(--light-grey);
        width: 1px;
        transform: translateY(-50%);
      }

      &.MuiInputAdornment-positionEnd {
        border-radius: 5px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border: solid 1px var(--input-box-border);
        border-left: none;
        background: var(--white);
        padding: 7px 0px;
        min-height: 36px;

        svg {
          fill: var(--dark-grey);
        }
      }
    }
  }

  &.date-picker {
    .MuiInputBase-formControl {
      .MuiInputBase-input {
        border-right: none;
        border-top-right-radius: 0px;
        border-bottom-right-radius: 0px;
        width: calc(100% - 35px);
      }
    }

    .MuiInputAdornment-root {
      margin-left: 0px;
      position: relative;

      &:before {
        content: "";
        left: -1px;
        top: 50%;
        height: 24px;
        position: absolute;
        background: var(--light-grey);
        width: 1px;
        transform: translateY(-50%);
      }

      .MuiIconButton-edgeEnd {
        border-radius: 5px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border: solid 1px var(--input-box-border);
        border-left: none;
        background: var(--white);
        padding: 7px;

        svg {
          fill: var(--dark-grey);
        }
      }
    }
  }

  &.form-control-autocomplete-1 {
    background: var(--white);
    height: 36px;
    padding: 9px 10px !important;
    font-size: 14px;
    color: #2e384d;
    font-weight: 400;
    border-radius: 5px;
    box-sizing: border-box;
    border: solid 1px var(--input-box-border);

    &:focus {
      box-shadow: none;
    }

    &::placeholder {
      color: var(--black);
      opacity: 1;
    }

    fieldset {
      display: none;
    }

    .MuiSelect-select {
      padding: 0px;
    }
  }

  &.read-only {
    .MuiInputLabel-root {
      font-weight: bold;
      margin-bottom: 6px;
    }

    .MuiInputBase-formControl {
      .MuiInputBase-input {
        border: none;
        background: transparent;
        padding: 0px 0 !important;
        font-weight: 400;
        height: auto;
        -webkit-text-fill-color: inherit;
      }
    }

    .MuiAutocomplete-endAdornment {
      display: none !important;
    }
  }

  &.autocomplete-no-label {
    .MuiInputLabel-root {
      display: none;
    }
  }

  .MuiInputLabel-root {
    color: var(--dark-grey) !important;
    transform: none;
    position: static;
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    margin-bottom: 8px;

    .MuiFormLabel-asterisk {
      color: #d32f2f;
      margin-left: 2px;
    }
  }

  .MuiInputBase-formControl {
    padding: 0 !important;

    .MuiInputBase-input {
      background: var(--white);
      height: 36px;
      padding: 9px 10px !important;
      font-size: 14px;
      color: #2e384d;
      font-weight: 400;
      border-radius: 5px;
      box-sizing: border-box;
      border: solid 1px var(--input-box-border);

      &:focus {
        box-shadow: none;
      }

      &::placeholder {
        color: var(--light-grey);
        opacity: 1;
      }
    }

    fieldset {
      display: none;
    }

    .MuiAutocomplete-endAdornment {
      top: 50%;
      transform: translateY(-50%);
      right: 0 !important;
      display: flex;

      .MuiAutocomplete-popupIndicator {
        position: relative;
        width: 40px;
        height: 40px;

        &:hover,
        &:focus {
          background: none;
        }

        &:before {
          position: absolute;
          content: "";
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 10px;
          height: 6px;
          background: url(../assets/svgs/autocomplete-down-arrow.svg) 0 0
            no-repeat;
        }

        svg {
          display: none;
        }

        .MuiTouchRipple-root {
          display: none;
        }
      }

      .MuiAutocomplete-clearIndicator {
        padding: 0;
        display: flex !important;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 12px;
        height: 40px;

        &:hover,
        &:focus {
          background: none;
        }

        .MuiTouchRipple-root {
          display: none;
        }
      }
    }
  }

  &.derived-autocomplete {
    .MuiAutocomplete-endAdornment {
      top: 50%;
      transform: translateY(-50%);
      right: 0 !important;
      display: flex;

      .MuiAutocomplete-popupIndicator {
        position: relative;
        width: 36px;
        height: 36px;
        background: transparent;

        &:hover,
        &:focus {
          // background: none;
        }

        &:before {
          position: absolute;
          content: "";
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 10px;
          height: 6px;
          background: url(../assets/svgs/autocomplete-down-arrow.svg) 0 0
            no-repeat;
        }

        svg {
          display: none;
        }

        .MuiTouchRipple-root {
          display: none;
        }
      }

      .MuiAutocomplete-clearIndicator {
        padding: 0;
        display: flex !important;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 12px;
        height: 40px;
        background-color: transparent;

        svg {
          color: var(--dark-grey);
        }

        &:hover,
        &:focus {
          background: none;
        }

        .MuiTouchRipple-root {
          display: none;
        }
      }
    }
  }
}
.pagination-not-required {
  .MuiDataGrid-footerContainer {
    display: none;
  }
}

.dataTable {
  border: solid 1px var(--table-border) !important;
  border-radius: 5px !important;
  padding-top: 16px;
  .MuiDataGrid-row {
    &.disabled-column {
      opacity: 0.5;
      pointer-events: none;
    }
  }
  &.no-buttons {
    padding-top: 0px;
    .MuiDataGrid-withBorderColor {
      border: none;
    }
  }

  &.filterMenuBox {
    .MuiDataGrid-columnHeaders {
      .MuiDataGrid-columnHeader {
        padding-left: 0px;
        padding-right: 0px;
        padding-bottom: 0px;

        &:first-child {
          padding-left: 0px;

          .textbox-group,
          .header-name {
            padding-left: 24px !important;
          }
        }

        &:last-child {
          padding-right: 0px;

          .textbox-group,
          .header-name {
            padding-right: 24px !important;
          }
        }

        .MuiDataGrid-columnHeaderTitleContainer {
          .MuiDataGrid-columnHeaderTitleContainerContent {
            width: 100%;
          }

          .MuiBox-root {
            width: 100%;

            .header-name {
              padding-left: 8px;
              padding-right: 8px;
              padding-bottom: 16px;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }

            .textbox-group-wrapper {
              height: auto;
              transition: 0.5s all ease-in;
              opacity: 1;

              &.hide {
                height: 0px;
                opacity: 0;
                display: none;
              }
            }

            .textbox-group {
              padding-top: 16px;
              padding-bottom: 16px;
              position: relative;
              background: white;
              display: flex;
              align-items: flex-start;
              padding-left: 8px;
              padding-right: 8px;

              &:before {
                content: "";
                position: absolute;
                top: 0px;
                left: 50%;
                transform: translateX(-50%);
                background: var(--input-box-border);
                height: 1px;
                width: 110%;
              }

              .MuiFormControl-root {
                .MuiInputBase-input {
                  border: solid 1px var(--input-box-border);
                  border-right: none;
                  border-top-left-radius: 4px;
                  border-bottom-left-radius: 4px;
                  font-size: 15px;
                  height: 40px;
                  box-sizing: border-box;

                  &::placeholder {
                    color: var(--light-grey);
                    opacity: 1;
                  }
                }

                fieldset {
                  display: none;
                }
              }

              .MuiIconButton-root {
                border: solid 1px var(--input-box-border);
                border-left: none;
                border-radius: 0px;
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
                height: 40px;
              }
            }
          }
        }
      }
    }

    .MuiDataGrid-columnSeparator {
      display: none;
    }
  }

  &.datatable-column-sep {
    .MuiDataGrid-pinnedColumns {
      min-height: auto !important;
    }
    .MuiDataGrid-virtualScrollerContent {
      min-height: inherit !important;
    }

    .MuiDataGrid-pinnedColumnHeaders {
      //border: solid 1px red;
      //background: red;
      //box-shadow: none;
      background: var(--table-header-bg);
      box-shadow: 0px 0px 1px -2px rgba(0, 0, 0, 0.2),
        0px 0px 2px 0px rgba(0, 0, 0, 0.14), 0px 0px 5px 0px rgba(0, 0, 0, 0.12);
    }
    &.pinnedColumnHeaders-bg-height {
      .MuiDataGrid-pinnedColumnHeaders--left {
        min-height: 100%;
      }
    }
    .data-grid-cell {
      padding: 0;
    }
    .MuiDataGrid-columnHeaders {
      .MuiDataGrid-columnHeader {
        padding-top: 8px !important;
        padding-bottom: 8px !important;
      }
      .MuiDataGrid-columnHeaderCheckbox {
        //display: none;
      }
    }
    .MuiDataGrid-row {
      .MuiDataGrid-cell {
        padding-top: 3px;
        padding-bottom: 3px;
        .btn-orange.btn-sm {
          padding: 1px 4px !important;
          height: 26px !important;
        }
      }
    }
    .MuiDataGrid-cell {
      border-left: solid 1px rgba(224, 224, 224, 1);

      &:first-child {
        border-left: none;
      }
    }
    .MuiDataGrid-columnHeader {
      border-left: solid 1px rgba(224, 224, 224, 1);
      &:first-child {
        border-left: none;
      }
    }
    .MuiDataGrid-cellCheckbox {
      .MuiButtonBase-root {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .PrivateSwitchBase-input {
        height: 26px;
        width: 38px;
      }
      .MuiTouchRipple-root {
        display: none;
      }
    }
  }

  [data-id^="auto-generated-row"] {
    button.groupby-expand-icon {
      display: none !important;
    }
  }

  &.no-radius {
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
  }

  &.pt-0 {
    padding-top: 0px;
  }

  &.bdr-top-0 {
    border-top: none !important;
  }

  .list-title {
    color: var(--orange);
    font-size: 18px;
    font-weight: 500;
    line-height: 20px;
  }

  .custom-toolbar {
    padding: 0px;
    margin-bottom: 16px;
  }

  .white-space-nowrap {
    span {
      white-space: nowrap !important;
      text-overflow: ellipsis;
      overflow: hidden;
      display: inline-block !important;
      width: 100%; // tried with 100%, working fine, if get issue change in px;
    }

    &.width-250 {
      span {
        width: 250px;
      }
    }
  }

  .MuiDataGrid-columnHeaders {
    background: var(--table-header-bg) !important;
    color: var(--table-head);
    border-top: solid 1px var(--table-border);
    font-size: 14px;
    border-radius: 0px;
    min-height: inherit !important;
    max-height: inherit !important;
    line-height: inherit !important;
    overflow: visible !important; // added this after pinned column issue
    .MuiDataGrid-columnHeaderTitle {
      font-weight: 500 !important;
    }

    .MuiDataGrid-columnHeader {
      flex: 1 0 auto;
      padding: 10px 8px;
      height: auto !important;
      &:first-child {
        padding-left: 24px;
      }

      &:last-child {
        padding-right: 24px;
      }

      &:focus {
        outline: none;
      }

      .MuiCheckbox-root {
        padding: 0px;
      }
      .MuiIconButton-root {
        float: left;
        padding-top: 0px;
        padding-bottom: 0px;
      }

      .MuiDataGrid-iconButtonContainer {
        .MuiIconButton-root {
          padding: 6px 6px;
          background: url(../assets/svgs/sort-icon.svg) right 50% no-repeat;

          .MuiSvgIcon-root,
          .MuiTouchRipple-root {
            display: none;
          }
        }
      }

      &[aria-sort="descending"].MuiDataGrid-columnHeader--sortable {
        .MuiDataGrid-iconButtonContainer {
          .MuiIconButton-root {
            background: url(../assets/svgs/sort-icon-down.svg) right 50%
              no-repeat;
          }
        }
      }

      &[aria-sort="ascending"].MuiDataGrid-columnHeader--sortable {
        .MuiDataGrid-iconButtonContainer {
          .MuiIconButton-root {
            background: url(../assets/svgs/sort-icon-up.svg) right 50% no-repeat;
          }
        }
      }
    }

    .MuiSvgIcon-root {
      color: var(--table-head);
      width: 1.25rem;
      height: 1.25rem;
    }

    .MuiDataGrid-columnSeparator {
      min-height: inherit !important;
    }
  }
  .MuiDataGrid-pinnedColumnHeaders {
    .MuiDataGrid-columnHeader {
      &:first-child {
        padding-left: inherit;
      }
      &:last-child {
        padding-right: inherit;
      }
    }
  }

  .MuiDataGrid-pinnedColumns {
    .MuiDataGrid-row {
      .MuiDataGrid-cell {
        &:first-child {
          padding-left: inherit;
        }
        &:last-child {
          padding-right: inherit;
        }
      }
    }
  }

  .MuiDataGrid-row {
    background: white;
    border-bottom: solid 1px var(--table-border);

    &:nth-child(even) {
      background: var(--table-row-alt-bg) !important;
    }

    &.changed-row {
      background: var(--dark-blue10) !important;
    }

    &.Mui-selected,
    &:hover,
    &:active {
      background: rgba(0, 0, 0, 0.04);
    }

    &:nth-child(even) {
      background: transparent;
    }

    .MuiDataGrid-cell {
      border-bottom-width: 0;
      font-size: 14px;
      padding: 4px;
      color: var(--dark-grey);
      white-space: normal;
      word-wrap: break-word;

      span {
        white-space: normal;
        word-wrap: break-word;
        display: block;
        max-width: 100%;
        &.text-ellipsis,
        .tooltip-ellipsis {
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
      }

      &:first-child {
        padding-left: 24px;

        .MuiIconButton-root.MuiIconButton-sizeSmall {
          position: relative;
          margin-left: -16px;

          &.custom-validation-arrow {
            margin-left: -11px;
            padding: 0px;
          }
        }
      }

      &:last-child {
        padding-right: 24px;
      }

      &:focus {
        outline: none;
      }

      .MuiIconButton-sizeMedium {
        padding: 4px 6px;

        &:first-child {
          padding-left: 0px;
        }

        &:last-child {
          padding-right: 0px;
        }
      }

      .MuiCheckbox-root {
        padding: 3px 9px;
      }

      .MuiOutlinedInput-root {
        height: 35px;
        background: white;

        .MuiSelect-outlined {
          line-height: 20px;
          padding-left: 10px;
          padding-right: 10px;
        }

        .MuiSvgIcon-root {
          position: relative;
          z-index: 1;
          top: calc(50% - 7px);
          transform: translateY(-50%);
        }
      }

      .MuiSelect-select {
        span {
          color: var(--dark-grey);
          position: relative;
          z-index: 1;
          font-size: 14px;
        }
      }

      fieldset {
        &[class*="MuiOutlinedInput"] {
          border: solid 1px var(--input-box-border);
          color: var(--dark-grey);
        }
      }

      .MuiCheckbox-root {
        > svg {
          color: var(--input-box-border);
          width: 1.25rem;
          height: 1.25rem;
        }

        &.Mui-checked {
          svg {
            color: var(--dark-blue);
          }
        }
      }

      .MuiInput-root {
        padding: 0;
        border: none;

        &.capitalize {
          select {
            //text-transform: capitalize;
          }
        }

        &:before,
        &:after {
          display: none;
        }

        &.input-ellipsis {
          input {
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            padding-right: 10px;
          }
        }

        .MuiInputBase-input {
          height: 36px;
          border: solid 1px var(--input-box-border);
          border-radius: 5px;
          box-sizing: border-box;
          padding: 5px 20px 5px 10px;
          background: #fff;
          font-size: 14px;
          color: var(--dark-grey);

          &::placeholder {
            color: var(--light-grey);
            opacity: 1;
          }
        }
      }

      .MuiIconButton-root {
        .MuiSvgIcon-root {
          width: 1.5rem;
          height: 1.5rem;
        }

        svg {
          color: var(--light-grey);
        }
      }
    }
  }

  .MuiDataGrid-footerContainer {
    border-top: none;
    font-size: 14px;
    padding-right: 22px;
    padding-left: 24px;
    min-height: 38px;
    .MuiToolbar-root {
      min-height: inherit;
    }
    .MuiTablePagination-displayedRows {
      margin: 0px;
    }

    .MuiTablePagination-selectLabel,
    .MuiInputBase-root,
    .MuiTablePagination-displayedRows {
      font-size: 14px;
    }

    .MuiTablePagination-actions {
      display: flex;
      align-items: center;
      column-gap: 8px;

      .MuiButtonBase-root {
        border-radius: 0px;
        border: solid 1px var(--dark-grey);
        padding: 2px;

        &.Mui-disabled {
          border: solid 1px var(--input-box-border);
        }
      }
    }
  }

  .MuiCircularProgress-root {
    color: var(--dark-grey);
  }

  .MuiDataGrid-detailPanel {
    background: var(--card-bg);
  }

  .rule-compare-list {
    .heading {
      padding-left: 5px;
      margin: 0 0 15px;
    }

    .table {
      border-collapse: collapse;

      td {
        padding: 5px;
      }
    }
  }

  .MuiDataGrid-iconButtonContainer {
    &[aria-label] {
      display: none;
    }
  }

  &.audit-grid {
    .MuiDataGrid-virtualScroller {
      height: auto;
    }
  }
}

.link-btn {
  color: var(--dark-blue);
  background: none;
  font-size: 12px;
  border: none;
  cursor: pointer;
}

%btn-base {
  background-color: var(--orange) !important;
  height: 36px !important;
  border-radius: 4px !important;
  font-size: 16px !important;
  line-height: 20px !important;
  padding: 8px 16px 8px 16px !important;
  text-transform: capitalize !important;
  color: white !important;
  font-weight: 400 !important;
  box-shadow: none !important;
  cursor: pointer;
}

.btn-orange {
  @extend %btn-base;

  &.min-width-auto {
    min-width: inherit;
  }

  .MuiTouchRipple-root {
    display: none;
  }

  &.btn-sm {
    height: 36px !important;
    line-height: 20px !important;
    padding: 9px !important;
    min-width: inherit !important;
  }

  &.btn-border {
    background: transparent !important;
    border: solid 1px var(--dark-blue) !important;
    color: var(--dark-blue) !important;

    &:disabled {
      border-color: var(--dark-blue50) !important;
      cursor: not-allowed;
      background: transparent !important;
      color: var(--dark-blue50) !important;
    }
  }

  &.btn-dark {
    background-color: var(--dark-grey) !important;
    border-color: var(--dark-grey) !important;
  }

  &.btn-blue {
    background-color: var(--dark-blue) !important;
    border-color: var(--white) !important;
    &:disabled {
      background-color: var(--dark-blue50) !important;
      cursor: not-allowed;
    }
  }

  &.btn-remove {
    height: 32px !important;
    background: white !important;
    padding: 7px 16px !important;
  }

  &.btn-no-border {
    border: none;
  }

  &.plus-btn-sm {
    padding: 3px 6px !important;
    height: 29px !important;
  }

  svg {
    max-height: 20px;
  }

  &:disabled {
    background-color: var(--orange-60) !important;
    //background: var(--light-grey) !important;
    //color: var(--dark-grey) !important;
    cursor: not-allowed;
  }
}

.btn-fixed-width {
  background-color: var(--badge-blue-border);
  border: none;
  border-radius: 4px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.derived-column {
  background-color: transparent !important;
  background-color: rgba(0, 0, 0, 0.04) !important;

  &.MuiDataGrid-row {
    &:hover,
    &:focus {
      background-color: transparent !important;
      background-color: rgba(0, 0, 0, 0.07) !important;
    }
  }
}

// .MuiDataGrid-detailPanel{
//   padding: 15px;
// }
.label-text {
  color: var(--dark-grey) !important;
  transform: none;
  position: static;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  margin-bottom: 8px;
  display: block;
  &.line-height16 {
    line-height: 16px;
  }

  &.pt-13 {
    padding-top: 13px;
  }

  &.pt-11 {
    padding-top: 11px;
  }

  &.mb-0 {
    margin-bottom: 0px;
  }

  &.newlabel {
    width: auto;
  }
}

.breadcrumb {
  padding: 0 !important;
  margin-bottom: 12px !important;

  ol {
    li {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: var(--orange);
      min-height: 24px;

      svg {
        position: relative;
        top: 2px;
      }

      &.MuiBreadcrumbs-separator {
        color: var(--black);
      }

      a {
        display: flex;
        align-items: center;
        color: var(--orange);
        text-decoration: none;

        &.active {
          color: var(--black);
        }

        .extra {
          display: inline-block;
          position: relative;
          padding-left: 30px;

          .icon-sep {
            position: absolute;
            top: 50%;
            transform: rotate(133deg) translateY(-50%);
            left: 19px;

            &:before {
              content: "";
              position: absolute;
              top: 0;
              left: 0px;
              width: 1.25px;
              height: 6px;
              background: var(--black);
            }

            &:after {
              content: "";
              position: absolute;
              top: 0;
              left: 0px;
              width: 6px;
              height: 1.25px;
              background: var(--black);
            }
          }
        }
      }

      .state-name {
        color: var(--black);
      }
    }
  }
}

.ml-5 {
  margin-left: 5px !important;
}
.max-width100 {
  max-width: 100%;
}

.m-0 {
  margin: 0 !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}
.mb-3 {
  margin-bottom: 1rem !important;
}
.pl-20 {
  padding-left: 20px;
}

.mb-6px {
  margin-bottom: 6px;
}
.no-padding-cell {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.p-0 {
  padding: 0 !important;
}
.d-flex {
  display: flex;
}
.justify-content-between {
  justify-content: space-between;
}
.justify-content-end {
  justify-content: flex-end;
}
.justify-content-center {
  justify-content: center;
}
.align-items-center {
  align-items: center;
}

.align-items-end {
  align-items: flex-end;
}
.cols-gap-6 {
  column-gap: 6px;
}
.cols-gap-8 {
  column-gap: 8px;
}

.childMargin > div {
  margin: 0px !important;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
  text-transform: uppercase;
  color: var(--black);
}

.text-box-card {
  padding: 24px 24px;
  border: solid 1px var(--table-border);
  background: var(--card-bg);
  margin-bottom: -21px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  z-index: 1;
  position: relative;
  &.list-page-card {
    padding: 16px 24px;
  }
  &.compact-text-box-card {
    padding: 12px 12px;
  }

  &.merge-zindex {
    position: relative;
    z-index: 9;
  }

  &.full-radius {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  &.no-radius {
    border-radius: 0;
  }

  &.top-radius {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  &.bottom-radius {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  &.mb-0 {
    margin-bottom: 0px !important;
  }
  &.mb-2 {
    margin-bottom: 16px !important;
  }
  &.mb-3 {
    margin-bottom: 24px !important;
  }

  &.bdr-bottom-0 {
    border-bottom-width: 0px;
  }

  &.pt-0 {
    padding-top: 0px;
  }

  &.bg-white {
    background: var(--white);
  }

  &.box-card-variables {
    padding: 12px 12px;
    margin-bottom: 12px;

    h3 {
      background: white;
      margin: -12px -12px 12px;
      padding: 12px 12px 12px;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      border-bottom: solid 1px var(--table-border);
      font-size: 13px;
      line-height: 1.1;
    }

    .inline-variables-parent {
      max-height: 200px;
      //min-height: 200px; // removed this class because generating extra space if there is only one data available
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: var(--light-grey);
        border-radius: 0px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--grey);
        border-radius: 0px;
      }
    }

    .inline-variables-table {
      background: var(--white);
    }
  }
}

.radio-group-gap {
  position: relative;
  top: -3px;

  .MuiRadio-root {
    padding: 0px 9px;

    .MuiSvgIcon-root {
      width: 1.25rem;
      height: 1.25rem;
    }
  }
}

.btn-export {
  font-size: 0px !important;
  color: transparent !important;

  .MuiButton-startIcon {
    margin-right: 0px;
  }
}

.btn-export-1 {
  padding: 8px !important;
  cursor: pointer;

  svg {
    width: 18px;

    .fill {
      fill: var(--dark-blue);
    }

    .stroke {
      stroke: var(--dark-blue);
    }
  }
}

.upload-loader {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #fff !important;
}

.ressearch-tabs-container {
  border-bottom: solid 1px var(--table-border);
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;

  &.no-bdr {
    border-bottom: none;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
  }
}

.mui-tabs-title {
  margin-top: 24px;
  margin-bottom: 0px;
  padding: 12px 16px;
  font-weight: 500;
  font-size: 16px;
  border-left: solid 1px var(--table-border);
  border-right: solid 1px var(--table-border);
  border-top: solid 1px var(--table-border);
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  background: var(--card-bg);
  &.mt-8 {
    margin-top: 8px;
  }
  &.mt-6 {
    margin-top: 6px;
  }
}

.mui-tabs {
  min-height: inherit !important;
  border-left: solid 1px var(--table-border);
  border-right: solid 1px var(--table-border);

  &.min-height-0 {
    min-height: 0px !important;
  }

  &.no-t-lr-radius {
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
  }

  &.research-query-tab {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 24px;
    background: white;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-top: solid 1px var(--table-border);

    .MuiTabs-flexContainer {
      .MuiTab-root.Mui-selected {
        font-weight: bold;
      }
    }
  }

  &.alternative-1 {
    background: white;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-top: solid 1px var(--table-border);

    .MuiTabs-flexContainer {
      .MuiTab-root.Mui-selected {
        font-weight: bold;
      }
    }
  }

  &.no-top-bdr-radius {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
  }

  .MuiTabs-flexContainer {
    padding-left: 24px;

    .MuiTabs-indicator {
      display: none;
    }

    .MuiTab-root {
      text-transform: capitalize;
      background: transparent;
      min-height: inherit;
      padding: 12px 0px;
      color: var(--light-grey);
      border-bottom: none;
      font-size: 16px;
      font-weight: 400;
      border-bottom: 4px solid transparent;

      & + .MuiTab-root {
        margin-left: 50px;
      }

      .MuiTouchRipple-root {
        display: none;
      }

      &:first-child {
        border-top-left-radius: 6px;
      }

      &:last-child {
        border-top-right-radius: 6px;
      }

      &.Mui-selected {
        border-color: var(--dark-blue);
        color: var(--black);
      }
    }
  }

  .MuiTabs-indicator {
    display: none;
  }
}

.text-box-card-white {
  padding: 12px 12px;
  border: solid 1px var(--table-border);
  background: transparent;
  margin-bottom: -21px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  z-index: 1;
  position: relative;

  .text-box-header {
    border-bottom: solid 1px var(--table-border);
    margin: -12px -12px 12px;
    padding: 2px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;

    &.alternative {
      padding: 12px 24px;
    }

    h3 {
      font-size: 16px;
      font-weight: 700;
      color: var(--dark-grey);
      margin: 0;
    }
  }

  .label-text {
    color: var(--dark-grey);
    font-size: 14px;
    font-weight: 700;
  }
}

.selectedChipTags {
  padding: 10px 0px;
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.refrence-checkbox {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.rule-h3 {
  font-size: 0.9rem;
  color: var(--black);
  font-weight: 600;
  line-height: 1.1;
  margin-top: 10px;
  margin-bottom: 10px;

  &.mt-0 {
    margin-top: 0px;
  }
}

.small-screen-navbar {
  @media (max-width: 600px) {
    width: 13px !important;
  }
}

.alert-msg {
  display: flex;
  align-items: center;
  padding: 4px !important;
  background: #e5e5e5 !important;
  color: var(--black) !important;

  .MuiAlert-icon {
    padding: 0;
  }

  .MuiAlert-message {
    padding: 0;
    line-height: 1.2;
  }
}

.page-view {
  border-left: solid 1px var(--card-border);
  border-top: solid 1px var(--card-border);
  padding: 12px 24px 6px 24px;
  min-height: calc(100vh - 64px);
}

.main-header {
  background: var(--card-bg) !important;
  .user-dropdown {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-transform: none;
    gap: 11px;
    .dropdown-arrow {
      display: none;
    }
  }

  .icon-notification {
    border: solid 1px var(--dark-blue);
  }

  .search-panel {
    .search-icon {
      color: var(--black);

      .svg_icons {
        transform: scale(1.8);
      }

      &:hover,
      &:focus {
        background: transparent;
      }

      .MuiTouchRipple-root {
        display: none;
      }
    }

    .search-textbox {
      padding: 4px 4px 4px 15px;
      font-size: 16px;

      input {
        &::placeholder {
          color: var(--black);
          opacity: 1;
        }
      }
    }
  }

  .username {
    font-size: 16px;
    line-height: 1.3;
    font-weight: 500;
    color: var(--black);
  }

  .occupation {
    font-size: 12px;
    line-height: 1.2;
    font-weight: 300;
    color: var(--grey);
  }
}

.common-search-panel {
  border: solid 1px var(--card-border);
  border-radius: 4px;
  background: white;
  padding-right: 8px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .search-textbox {
    flex: 1 1 0;

    input {
      height: 16px;
      padding: 9px 10px;
      color: var(--dark-grey);
      font-size: 15px;

      &::placeholder {
        opacity: 1;
        color: var(--light-grey);
      }
    }
  }

  .search-icon {
    padding: 0;
    color: var(--dark-grey);
    position: relative;

    .svg_icons {
      transform: scale(1.1);
    }

    &:before {
      content: "";
      left: -10px;
      top: 50%;
      height: 24px;
      position: absolute;
      background: var(--light-grey);
      width: 1px;
      transform: translateY(-50%);
    }
  }
}

.datagrid-action-btn {
  &:hover {
    background-color: transparent !important;
  }

  .MuiTouchRipple-root {
    display: none;
  }

  &.min-width-40 {
    min-width: 38px;
  }
}

.btn-nostyle {
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 0;

  img {
    display: block;
  }
}

.add-rule-accordion {
  border: solid 1px var(--table-border) !important;

  & + .add-rule-accordion {
    border-top: none !important;
  }

  .MuiAccordionSummary-root {
    background: transparent;
    padding-left: 24px;
    padding-right: 24px;

    .MuiAccordionSummary-content {
      color: var(--black);
      font-size: 18px;
      font-weight: 500;
      margin-top: 8px;
      margin-bottom: 8px;
    }
  }
}

.table-filters {
  border-collapse: collapse;
  width: 100%;

  tr {
    td {
      background: var(--card-bg);
      padding-top: 16px;
      padding-bottom: 16px;
      padding-left: 12px;
      padding-right: 12px;
      vertical-align: top;

      &.bg-white {
        background: var(--white);
      }

      &.valign-center {
        vertical-align: middle;
      }
      &.align-right {
        text-align: right !important;
      }

      button {
        padding: 6px;
      }

      .number {
        padding: 10px 0 3px 7px;
        display: block;
      }
    }

    & + tr {
      td {
        border-top: solid 1px white;
      }
    }
  }

  &.white-bg {
    tr {
      &:last-child {
        td {
          border-bottom: none;
        }
      }
    }

    td {
      border-bottom: solid 1px var(--table-border);
      border-right: solid 1px var(--table-border);
      text-align: left;
      padding: 6px 12px;

      &:last-child {
        border-right: none;
      }
    }
  }
}

.table-filter-bg {
  background: var(--white);
}

.table-filters-parent {
  border: solid 1px var(--input-box-border);
  border-radius: 4px;
  overflow: hidden;
}

.MuiCollapse-vertical {
  .table-filters-parent {
    table:not(.table) {
      border: none;

      td {
        border-left: none;
      }
    }
  }
}

.border-0 {
  border: none;

  &:focus {
    outline: none;
  }
}

%mui-custom-chips {
  .align-from-left {
    .MuiChip-filled {
      margin-right: 6px !important;

      & + .MuiChip-filled {
        margin-left: 0px !important;
      }
    }

    &.flex-reverse {
      .MuiChip-filled {
        flex-direction: row-reverse;

        &.sep-position {
          &:before {
            right: 57px;
          }
        }

        .MuiChip-avatar {
          margin-right: 0px;
          display: flex;
          align-items: center;
        }

        &:after {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 27px;
          width: 1px;
          height: 18px;
          background: var(--white);
          content: "";
        }

        &:before {
          right: 26px;
        }

        .MuiIconButton-sizeSmall {
          padding-right: 3px;
          margin-right: 0px;
        }

        .MuiChip-deleteIcon {
          padding-left: 6px;
        }

        .MuiChip-label {
          padding-left: 6px;
          padding-right: 6px;
        }
      }
    }
  }

  &.autocomplete-chips-view-only {
    .MuiChip-filled {
      &:before {
        display: none;
      }
    }

    .MuiInputBase-input {
      display: none;
    }

    .MuiAutocomplete-endAdornment {
      display: none;
    }

    .Mui-disabled {
      opacity: 1;
    }

    .MuiChip-deleteIcon {
      display: none;
    }
  }

  .MuiInputBase-colorPrimary {
    &.MuiInputBase-fullWidth {
      display: flex;
      align-items: flex-end;
      flex-flow: wrap-reverse;
      width: 100%;

      .MuiInputBase-input {
        &.MuiOutlinedInput-input {
          width: 100%;
        }
      }
    }
  }

  .MuiAutocomplete-endAdornment {
    top: 19px !important;
  }

  .MuiChip-avatar {
    width: auto !important;
  }

  .counter {
    padding-left: 5px;
    padding-right: 8px;
    font-size: 14px;
    font-family: "Inter", Arial, Helvetica, sans-serif;
    font-weight: 500;
  }

  .MuiChip-filled {
    margin: 3px 0 0 !important;
    border-radius: 4px;
    background: var(--chip-bg);
    height: auto;
    min-height: 30px;
    position: relative;
    font-size: 14px;
    color: var(--dark-grey);
    font-weight: 500;

    &:before {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 24px;
      width: 1px;
      height: 18px;
      background: var(--white);
      content: "";
    }

    & + .MuiChip-filled {
      margin-left: 6px !important;
    }

    .MuiChip-deleteIcon {
      color: var(--chip-close-bg);
      font-size: 15px;
      margin-left: 0px;

      &:hover {
        color: var(--chip-close-bg);
      }
    }
  }
}
.MuiAutocomplete-root {
  .autocomplete-chips-direction {
    .align-from-left {
      .MuiChip-filled {
        .MuiChip-label {
          word-break: break-all;
          white-space: normal !important;
          padding-top: 4px;
          padding-bottom: 4px;
        }
      }
    }
  }
}

.autocomplete-chips-direction {
  @extend %mui-custom-chips;
}

.form-control-autocomplete {
  &.capitalize {
    input {
      text-transform: capitalize;
    }
  }
  &.derived-autocomplete {
    @extend %mui-custom-chips;

    .MuiChip-filled,
    .autocomplete-chips-direction {
      .MuiChip-filled {
        padding: 0px;

        &:hover {
          background: var(--chip-bg);
        }
      }
    }
  }

  &.alternative-2 {
    .autocomplete-chips-direction {
      .MuiChip-filled {
        .MuiChip-deleteIcon {
          display: flex;
          align-items: center;

          button {
            padding: 0px;
          }

          svg {
            width: 16px;
            height: 16px;
          }
        }
      }
    }
  }
}
.secondary-resource-gap {
  padding-left: 10px;
}
.secondary-resource-sep {
  position: absolute;
  top: -17px;
  left: -12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  span {
    border-left: 2px dashed var(--orange);
    height: 20px;
    width: 2px;
    display: block;
  }
  .connector-top {
    svg {
      display: block;
      position: relative;
      top: -2px;
    }
  }
  &:after {
    content: "";
    position: absolute;
    bottom: -9px;
    left: 50%;
    width: 6px;
    height: 6px;
    background: var(--orange);
    border-radius: 6px;
    transform: translateX(-50%);
  }
}

.tags-tooltip-parent {
  display: flex;
  align-items: center;
  column-gap: 6px;
  row-gap: 6px;
  flex-wrap: wrap;

  & .chip {
    font-size: 14px;
    background: var(--chip-bg);
    border-radius: 4px;
    padding: 4px 8px;
    color: var(--dark-grey);
    font-family: "Inter", Arial, Helvetica, sans-serif;
    font-weight: 500;
  }

  .counter {
    padding-left: 5px;
    padding-right: 8px;
    font-size: 14px;
    font-family: "Inter", Arial, Helvetica, sans-serif;
    font-weight: 500;
  }
}

.mergetag-popup {
  position: absolute;
  top: 0px;
  right: 0px;
  width: 336px;
  height: auto;
  background: var(--white);
  border: solid 1px var(--input-box-border);
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  z-index: 9;
  padding: 24px 32px;
  display: none;

  .autocomplete-wrapper {
    margin-bottom: 18px;
    position: relative;
    padding-bottom: 24px;

    &:after {
      content: "";
      position: absolute;
      bottom: 0;
      left: -32px;
      width: calc(100% + 64px);
      height: 1px;
      background: var(--input-box-border);
    }
  }

  .table-draggable {
    border: none;

    .MuiDataGrid-cell {
      &:focus-within {
        outline: none;
      }
    }

    .MuiDataGrid-withBorderColor {
      border-color: var(--input-box-border) !important;
    }

    .MuiCheckbox-root {
      padding-left: 4px;
      padding-right: 4px;
    }

    .MuiDataGrid-rowReorderCell {
      background: url(../assets/svgs/icon_dragabble.svg) 50% 50% no-repeat;

      svg {
        display: none;
      }
    }
  }
}

.cursor-pointer {
  cursor: pointer;
}
.cursor-default {
  cursor: default;
  pointer-events: none;
}

.error-text {
  font-size: 10px;
  color: #d44c32;
}

.refrence-close-icon {
  position: relative;
  background: none;
  border: none;
  width: 14px;
  height: 14px;
  cursor: pointer;

  &:before,
  &:after {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    background: var(--black);
    content: "";
  }

  &:before {
    width: 1px;
    height: 16px;
  }

  &:after {
    width: 16px;
    height: 1px;
  }
}

.column-validation-box {
  border: solid 1px var(--table-border) !important;

  & + .column-validation-box {
    margin-top: 20px;
  }

  .column-validation-item {
    & + .column-validation-item {
      margin-top: 20px;
    }
  }

  h4 {
    background: var(--card-bg);
    margin: -16px -16px 16px;
    padding: 10px;
    border-bottom: solid 1px var(--table-border);
  }

  h5 {
    margin: 0 0 5px;
  }
}

.accordion-panel {
  &.accordion-group-parents {
    .MuiPaper-elevation {
      overflow: hidden;

      & > [id*="header"] {
        background: var(--card-bg);
      }

      & + .MuiPaper-elevation {
        margin-top: 8px !important;
      }
    }
  }

  &.accordion-group {
    &:first-child {
      .MuiPaper-elevation {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
      }
    }

    &:last-child {
      .MuiPaper-elevation {
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }

    .MuiPaper-elevation {
      border-radius: 0;

      & > [id*="header"] {
        background: transparent;
      }
    }

    & + .accordion-group {
      .MuiPaper-elevation {
        margin-top: 0px !important;
        border-top: none;
      }
    }
  }

  &.alternative {
    .MuiCollapse-vertical {
      &:before {
        content: "";
        height: 1px;
        background: var(--table-border);
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
      }

      &:after {
        display: none;
      }
    }
  }

  .MuiPaper-elevation {
    box-shadow: none;
    border: solid 1px var(--table-border);
    border-radius: 4px;

    & + .MuiPaper-elevation {
      margin-top: 6px !important;

      &:before {
        display: none;
      }
    }
    &.Mui-expanded {
      margin: 8px 0px;
    }

    &.box-shadow {
      box-shadow: 1px 2px 3px rgba(0, 0, 0, 0.1);
      &:before {
        display: none;
      }
    }
    &.mb-8 {
      margin-bottom: 8px;
    }

    &.mt-16 {
      margin-top: 16px !important;
    }
    &.ml-16 {
      margin-left: 55px !important;
    }
    &.mt-8 {
      margin-top: 8px !important;
    }
    &.mt-6 {
      margin-top: 6px !important;
    }

    &.no-before-shadow {
      &:before {
        display: none;
      }
    }

    &.heading-bold {
      .MuiAccordionSummary-content {
        font-weight: bold;
        font-size: 14px;
      }
    }
    .MuiAccordionDetails-root {
      padding: 8px;
    }
  }

  .custom-actions {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;

    .action-btns {
      display: flex;
      align-items: center;
      &.mr-15 {
        margin-right: 15px;
      }

      .view-icon {
        border: none;
        background: transparent;
        cursor: pointer;

        svg {
          display: block;
          width: 24px;

          path {
            stroke: rgba(0, 0, 0, 0.8);
          }
        }
      }
    }
  }

  h4 {
    margin: 0px;
  }

  [id*="header"],
  .min-header {
    position: relative;
    min-height: 40px !important;
    padding-left: 12px;
    padding-right: 12px;

    & > div {
      margin: 0px;
    }
  }

  &.mt-16 {
    margin-top: 16px;
  }
  &.mt-8 {
    margin-top: 8px;
  }

  .MuiAccordionSummary-content.Mui-expanded {
    margin: 0;
  }
}

.MuiCollapse-vertical {
  position: relative;

  &:after {
    content: "";
    height: 1px;
    background: var(--table-border);
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
  }

  table:not(.table) {
    border-top: solid 1px var(--table-border);
    border-right: solid 1px var(--table-border);
    width: 100%;
    border-collapse: collapse;
    .align-middle {
      vertical-align: middle;
    }

    th {
      background: var(--card-bg);
      &.no-left-border {
        border-left: none;
      }
    }

    th,
    td {
      border-bottom: solid 1px var(--table-border);
      border-left: solid 1px var(--table-border);
      padding: 6px 12px;
      text-align: left;
    }

    ul {
      margin: 0px;

      //padding:0px;
      li {
        //list-style-type: none;
        & + li {
          margin-top: 15px;
        }
      }
    }

    h5 {
      margin: 0px;
    }
  }

  .nested-accordion {
    border-top: none;
    border-left: none;
    border-right: none;
    border-radius: 0px;
    margin-left: 15px !important;
    margin-right: 15px !important;

    &.Mui-expanded {
      .MuiCollapse-vertical {
        &:after {
          display: none;
        }
      }
    }

    .MuiCollapse-entered {
      .MuiCollapse-vertical {
        &:after {
          display: none;
        }
      }
    }

    &:last-of-type {
      border-radius: 0px;
    }

    margin-top: 8px;

    [id*="header"] {
      padding-left: 0px;
      padding-right: 0px;
      font-style: italic;
    }

    .MuiAccordionDetails-root {
      padding-left: 0px;
      padding-right: 0px;
    }
  }
}

.badges {
  padding: 4.5px 9px;
  border-radius: 4px;
  font-size: 14px;
  color: var(--dark-grey);

  &.success-bg {
    background-color: var(--success-bg);
  }

  &.failure-bg {
    background-color: var(--failure-bg);
  }
}

.rule-execution {
  padding: 12px 12px 0px 12px;
  background: var(--card-bg);

  @media (max-width: 1199.98px) {
    padding: 35px 0 30px 25px;
  }

  &.rule-execution-history {
    padding: 12px 12px 0px 12px;
  }

  h4:not(.acc-h4) {
    font-size: 15px;
    margin: 0 0 16px;
    font-weight: 700;

    &.mb10 {
      margin-bottom: 10px;
    }

    &.mb-0 {
      margin-bottom: 0px;
    }
  }

  h5 {
    text-transform: uppercase;
    font-size: 15px;
    margin: 0 0 0px;
    font-weight: 700;
  }

  .p {
    font-size: 15px;
    font-weight: 500;
    font-style: italic;
    margin: 0;

    &.no-italic {
      font-style: normal;
      font-weight: 400;
    }
  }

  .MuiAccordionSummary-root {
    min-height: inherit;
  }

  &.italic-none {
    .MuiCollapse-vertical {
      font-style: normal;
    }
  }

  .MuiCollapse-vertical {
    position: relative;
    font-size: 15px;
    line-height: 1.2;
    font-weight: 500;
    font-style: italic;

    &:after {
      display: none;
    }

    .list-item-group {
      margin-top: 10px;
    }

    .list-item {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      padding-right: 37px;
      align-items: center;

      .record-count {
        min-width: 45px;
        text-align: right;
      }
    }
  }

  [class*="MuiAccordionSummary-content"] {
    &.Mui-expanded {
      margin: 0px;
    }
  }

  .resource-table {
    width: 100%;
    border-collapse: collapse;
    td {
      padding: 0px;
    }

    .first-col {
      padding-right: 30px;
    }
  }

  .text-box-card {
    background: var(--white);
    padding: 16px 12px;
    overflow: hidden;
    height: 100%;
    margin-bottom: 0px;

    .h4 {
      background: var(--card-bg);
      margin: -16px -16px 6px;
      padding: 8px 16px;
      border-bottom: solid 1px var(--table-border);

      &.mb-0 {
        margin-bottom: 0px;
      }
    }
  }
}

.base-resource-checkbox {
  display: flex;
  align-items: center;
  column-gap: 5px;
  margin-top: 15px;

  .MuiCheckbox-root {
    padding: 0px !important;
  }
}

.rule-execution-page {
  background: white;
  &.compact-text-box-card {
    .text-box-card {
      padding: 12px 12px;
      &.pt-0 {
        padding-top: 0px;
      }
      .page-title {
        &:after {
          left: -12px;
          width: calc(100% + 24px);
        }
      }
    }
  }

  .page-title {
    color: var(--black);
    font-size: 14px;
    font-weight: 500;
    position: relative;
    margin: 0 0 12px;
    line-height: 1.43;
    padding: 10px 0 10px;

    span {
      display: inline-block;
      padding-left: 5px;
      color: var(--orange);
    }

    &.before {
      &:before {
        content: "";
        position: absolute;
        top: 0px;
        left: -24px;
        width: calc(100% + 48px);
        height: 1px;
        background: var(--input-box-border);
      }
    }

    &:after {
      content: "";
      position: absolute;
      bottom: 0px;
      left: -24px;
      width: calc(100% + 48px);
      height: 1px;
      background: var(--input-box-border);
    }
  }

  h4 {
    font-size: 14px;
    color: var(--table-head);
    margin: 0px 0px 12px;
    &.heading-hr {
      position: relative;
      margin-bottom: 5px;
      &:before {
        background: var(--input-box-border);
        position: absolute;
        bottom: -4px;
        left: 0px;
        width: 100%;
        height: 1px;
        content: "";
        display: none;
      }
    }
  }

  .table {
    margin: 0px;
    padding: 0px;
    border-collapse: collapse;

    &.w-100 {
      width: 100%;
    }

    &.table-resource-filter {
      border-top: solid 1px var(--input-box-border);
      border-left: solid 1px var(--input-box-border);
      th,
      td {
        border-bottom: solid 1px var(--input-box-border);
        border-right: solid 1px var(--input-box-border);
        padding: 5px;
        background: var(--white);
      }
      td {
        vertical-align: top;
      }
      .width-180 {
        width: 180px;
      }
    }

    th,
    td {
      font-size: 14px;
      color: var(--dark-grey);
      text-align: left;
      padding: 0px;
    }

    th {
      font-weight: 600;
    }

    .pe-40 {
      padding-right: 40px;
    }

    .pe-20 {
      padding-right: 20px;
    }

    .mb-24 {
      margin-bottom: 24px;
    }
  }
}

.min-height-32 {
  min-height: 32px;
}

.break-word {
  word-wrap: break-word;
}

.word-break-all {
  word-break: break-all;
}

.w-240 {
  width: 240px;
}

.w-280 {
  width: 280px;
}

.w-320 {
  width: 320px;
}

.long-string-width {
  max-width: 100%;
}

textarea {
  resize: none;
}

.Toastify__toast-container {
  .Toastify__toast {
    .Toastify__toast-body {
      align-items: flex-start;

      .Toastify__toast-icon {
        position: relative;
        top: 1px;
      }
    }
  }
}

.rule-domain-chart-legends {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 24px;

  span {
    & + span {
      margin-left: 11px;
    }

    label {
      margin-right: 7px;
    }
  }
}

.rule-domain-chart-box {
  border-radius: 4px;
  border: solid 1px var(--table-border);
  padding: 0px 11px 20px 11px;

  h3 {
    margin: 0 0 12px;
    padding: 6px 0;
    position: relative;

    &:before {
      content: "";
      position: absolute;
      bottom: 0px;
      height: 1px;
      width: calc(100% + 22px);
      left: -11px;
      background: var(--table-border);
    }
  }

  .data-name {
    color: var(--light-grey);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 18px;
  }

  .legends {
    display: flex;
    margin-bottom: 4px;
    column-gap: 16px;

    span {
      display: flex;
      align-items: center;
      font-size: 12px;
      padding-top: 1px;
      padding-bottom: 1px;

      & + span {
        margin-left: 0px;
      }

      label {
        margin-right: 7px;
      }
    }
  }

  .records {
    font-size: 12px;
    color: var(--grey);
  }
}

.validate-page {
  .heading {
    display: flex;
    align-items: center;
    column-gap: 12px;

    h3 {
      color: var(--black);
      font-size: 18px;
      font-weight: 400;
      margin: 0;
    }
  }
  .header-group {
    margin-bottom: 24px;

    @media (min-width: 992px) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 14px;
    }
    &.has-base-resource {
      @media (min-width: 992px) {
        flex-wrap: wrap-reverse;
      }
      .column-right {
        width: 100%;
        justify-content: flex-end;
      }
    }

    h3 {
      font-size: 18px;
      font-weight: 400;
      color: var(--black);
      margin: 0;

      @media (max-width: 991.98px) {
        margin-bottom: 15px;
      }

      strong {
        font-weight: 500;
      }
    }
    .base-resource-validation-result {
      & a {
        text-decoration: none;
        color: var(--white);
      }
    }

    .column-right {
      @media (min-width: 768px) {
        display: flex;
        align-items: center;
        column-gap: 14px;
      }

      .time-block {
        display: flex;
        column-gap: 14px;

        @media (max-width: 767.98px) {
          margin-bottom: 15px;
        }
      }
      .timestamp-info {
        background: var(--chip-bg);
        color: var(--black);
        font-size: 16px;
        font-weight: 400;
        display: flex;
        column-gap: 8px;
        border-radius: 24px;
        padding: 0 10px;
      }

      .text {
        font-size: 16px;
        font-weight: 500;
        color: var(--black);
      }
    }

    .badges-group {
      display: flex;
      column-gap: 14px;
      align-items: center;

      @media (max-width: 767.98px) {
        column-gap: 6px;
      }
    }

    .badges {
      height: 25px;
      border-radius: 13px;
      border-width: 1px;
      border-style: solid;
      text-transform: capitalize;

      &.badge-base {
        font-size: 14px;
        font-weight: 400;
        color: var(--white);
        line-height: 1;

        @media (max-width: 767.98px) {
          font-size: 12px;
          padding-left: 4px;
          padding-right: 4px;
        }

        &.badge-blue {
          background: var(--badge-blue-border-20);
          border-color: var(--badge-blue-border);
          color: var(--black);
        }

        &.badge-orange {
          background: var(--orange-20);
          border-color: var(--orange);
          color: var(--black);
        }

        &.badge-error {
          background: var(--badge-danger-bg);
          border-color: var(--badge-danger-bg);
        }

        &.badge-success {
          background: var(--success-bg);
          border-color: var(--black);
          color: var(--black);
        }
      }
    }
  }

  .additional-props {
    h4 {
      font-size: 16px;
      font-weight: 400;
      color: var(--blue-1);
      margin: 0 0 18px;
    }

    .additional-box {
      border: solid 1px var(--input-box-border);
      padding: 0px;
      border-radius: 4px;
      background: var(--white);

      .additional-header {
        color: var(--black);
        font-size: 16px;
        font-weight: 400;
        padding: 7px 6px 7px 16px;
        border-bottom: solid 1px var(--input-box-border);
        color: var(--black);
        text-transform: capitalize;
      }

      .additional-desc {
        padding: 16px;

        p {
          font-size: 14px;
          font-weight: 400;
          color: var(--gray-1);
          margin: 0;

          span {
            font-weight: 700;
            color: var(--black);
          }

          small {
            font-size: 12px;
            font-style: italic;
            word-wrap: break-word;
          }
        }
      }
    }
  }
}

.dataListTitle {
  font-size: 16px;
  font-weight: 400;
  color: var(--blue-1);
  margin-bottom: 18px;
}

.execution-history-btn-group {
  position: relative;

  .transaction-btn-group {
    position: absolute;
    top: -36px;
    right: 0;
    display: flex;
    column-gap: 16px;
  }

  .filters-btn {
    display: flex;
    align-items: center;
  }

  .MuiCollapse-vertical {
    &:after {
      display: none;
    }
  }
}

.MuiPickersLayout-contentWrapper {
  .MuiDateCalendar-root {
    width: 300px;
  }

  .MuiDayCalendar-slideTransition {
    min-height: 200px;
  }

  .MuiPickersDay-root,
  .MuiDayCalendar-weekDayLabel {
    font-size: 14px;
  }

  .MuiPickersDay-root {
    &.Mui-selected {
      &:hover,
      &:focus {
        background: #f0f0f0;
      }
    }
  }
}

.wide-tooltip {
  &.w-380 {
    width: 380px !important;
    max-width: 380px !important;
  }

  &.w-250 {
    width: 250px !important;
    max-width: 250px !important;
  }

  pre {
    &::-webkit-scrollbar {
      width: 10px;
    }

    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.7);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.7);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 1);
    }
  }
}

.text-bold {
  font-weight: bold;
}

.required-asterisk {
  color: #d32f2f;
  margin-left: 2px;
}

.validation-error {
  color: #d32f2f;
  font-size: 11px;
  margin-left: 14px;
  &.ml-0 {
    margin-left: 0;
  }
  .ml-14 {
    margin-left: 14px;
  }
}

.MuiDataGrid-row {
  .rotate-arrow {
    .MuiSvgIcon-root {
      transition: 0.1s all ease-in;
    }
  }
}

.MuiDataGrid-row--detailPanelExpanded {
  .rotate-arrow {
    .MuiSvgIcon-root {
      transform: rotate(180deg);
    }
  }
}

.validation-column-table {
  display: flex;
  padding-right: 14px;

  h3 {
    margin: 0 0 10px;
  }

  .td-column {
    padding: 0 8px 0 8px;
  }

  .cost-row {
    & + .cost-row {
      margin-top: 11px;
    }
  }
}

.adhock-query-accordion-wrapper {
  border: solid 1px var(--table-border);
  padding: 0px 16px;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.validation-item-box {
  background: var(--white);
  border-radius: 4px;
  border: solid 1px var(--table-border);
  padding: 6px 15px 6px 15px;
  position: relative;
  box-shadow: 1px 2px 3px rgba(0, 0, 0, 0.1);
  min-height: 65px;

  &.min-height {
    min-height: 85px;
  }

  &.btns-pad {
    padding-right: 35px;
  }

  .action-btns {
    position: absolute;
    top: 0px;
    right: 0px;
    background: rgb(25 107 180);
    bottom: 0px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    gap: 5px;

    &.gap-25 {
      gap: 25px;
    }

    .edit-btn {
      padding: 3px;
      cursor: pointer;

      svg {
        fill: var(--white);
        width: 18px;
        height: 18px;
        display: block;
      }
    }

    .delete-btn {
      padding: 3px;
      cursor: pointer;

      svg {
        stroke: var(--white);
        width: 14px;
        height: 14px;
        display: block;
      }
    }
  }
}

%error-border-placeholder {
  border: 1px solid #d32f2f !important;

  &::placeholder {
    color: #d32f2f !important;
  }
}

.border-red {
  @extend %error-border-placeholder;
}

.border-red-fieldset {
  fieldset[class*="MuiOutlinedInput"] {
    border-color: #d32f2f !important;
  }
}

.border-red-parent {
  .MuiInputBase-input {
    @extend %error-border-placeholder;

    span {
      color: #d32f2f !important;
    }
  }
}

.has-error {
  border-color: #d32f2f !important;

  &::placeholder {
    color: #d32f2f !important;
  }

  .MuiInputBase-formControl {
    .MuiInputBase-input {
      border-color: #d32f2f !important;

      &::placeholder {
        color: #d32f2f !important;
      }
    }
  }

  .ace_placeholder {
    opacity: 1;
    color: #d32f2f !important;
  }
}

.input-ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dashboard-title-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  &.mb-12 {
    margin-bottom: 12px;
  }
  &.mb-6 {
    margin-bottom: 6px;
  }
  &.mb-0 {
    margin-bottom: 0px;
  }

  &.flex-end {
    justify-content: flex-end;
  }

  .heading {
    display: flex;
    align-items: center;
    column-gap: 12px;

    h3 {
      color: var(--black);
      font-size: 18px;
      font-weight: 400;
      margin: 0;
    }
  }

  .right-column {
    display: flex;
    align-items: center;
    column-gap: 20px;

    &.column-gap-12 {
      column-gap: 12px;
    }

    .watch-info {
      color: var(--black);
      font-size: 16px;
      font-weight: 500;
      display: flex;
      align-items: center;
      column-gap: 8px;

      svg {
        display: block;
      }
    }
    .timestamp-info {
      background: var(--chip-bg);
      color: var(--black);
      font-size: 16px;
      font-weight: 400;
      display: flex;
      column-gap: 8px;
      border-radius: 24px;
      padding: 0 10px;
    }

    .badge {
      background: transparent;
      color: white;
      color: var(--white);
      font-size: 14px;
      border-radius: 24px;
      height: 25px;
      padding-left: 12px;
      padding-right: 12px;
      text-transform: capitalize;
      display: flex;
      align-items: center;

      &.failed {
        background: var(--badge-danger-bg);
      }

      &.success {
        background: var(--success-bg-1);
      }
    }

    .download {
      svg {
        display: block;
      }
    }
  }
}

.word-wrap-break-word {
  word-wrap: break-word;
}
.text-capitalize {
  text-transform: capitalize;
}

[data-testid="sentinelStart"] {
  & + .MuiDataGrid-panelWrapper {
    .MuiDataGrid-panelContent {
      .MuiDataGrid-filterForm {
        column-gap: 14px;
        padding: 21px 21px 0;

        .MuiInputLabel-formControl {
          color: var(--dark-grey) !important;
          font-size: 14px;
          line-height: 1.2;
          transform: none;
          margin-bottom: 8px;
          position: static;
        }

        .MuiDataGrid-filterFormValueInput {
          input {
            padding: 5.5px 4px 7.5px 5px;
          }

          fieldset {
            display: none;
          }
        }

        .MuiDataGrid-filterFormColumnInput,
        .MuiDataGrid-filterFormOperatorInput,
        .MuiDataGrid-filterFormValueInput {
          width: 150px;
        }

        .MuiDataGrid-filterFormColumnInput,
        .MuiDataGrid-filterFormOperatorInput {
          .MuiInputBase-formControl {
            &:before {
              content: "";
              position: absolute;
              background: var(--gray-1);
              height: 24px;
              width: 1px;
              content: "";
              display: block;
              top: 4px;
              right: 28px;
              left: inherit;
            }
          }
        }

        .MuiInputBase-formControl {
          height: 35px;
          border: solid 1px var(--input-box-border);
          border-radius: 5px;
          box-sizing: border-box;
          padding: 0;
          background: #fff;
          font-size: 14px;
          color: var(--dark-grey);
          margin-top: 0px;

          select {
            padding: 9px 44px 10px 10px;

            &:focus {
              background: none;
            }
          }

          &:after,
          &:before {
            display: none;
          }

          &::placeholder {
            color: var(--dark-grey);
            opacity: 1;
          }
        }

        .MuiNativeSelect-icon {
          top: 0px;
          height: 33px;
          right: 4px;
        }
      }

      .MuiDataGrid-filterFormDeleteIcon {
        order: 4;

        button {
          background: var(--chip-close-bg);
          border-radius: 50%;
          color: var(--white);
          padding: 2px;
          position: relative;
          top: -5px;
        }
      }
    }

    .MuiDataGrid-panelFooter {
      padding: 21px 60px 21px 21px !important;

      .MuiButton-textPrimary {
        @extend %btn-base;
        font-size: 14px !important;
        background: transparent !important;
        border: solid 1px var(--dark-blue) !important;
        color: var(--dark-blue) !important;
      }
    }
  }
}

// .body100, .html100{
//   height:100%;
// }
html,
body {
  height: 100%;
}

.html100 {
  height: 100%;
}

.body100 {
  height: 100%;
}

.w-100 {
  width: 100%;
}

.w-160 {
  width: 160px;
}

.w-180 {
  width: 180px;
}

.unprotected-pages {
  background: url(../assets/sign-up.jpg) 50% 50% no-repeat;
  background-size: cover;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: auto;

  @media (max-width: 767.98px) {
    justify-content: center;
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .column-right {
    width: 50%;
    padding-left: 15%;
    padding-right: 15%;
    background: var(--white);
    margin: 0;
    display: flex;
    align-items: center;
    min-height: 100vh;
    padding-top: 30px;
    padding-bottom: 30px;

    @media (max-width: 1399.98px) {
      padding-left: 10%;
      padding-right: 10%;
    }

    @media (max-width: 1199.98px) {
      padding-left: 7%;
      padding-right: 7%;
    }

    @media (max-width: 991.98px) {
      padding-left: 30px;
      padding-right: 30px;
    }

    @media (max-width: 767.98px) {
      padding: 30px;
      height: auto;
      width: 400px;
      max-width: calc(100vw - 60px);
      border-radius: 15px;
      box-shadow: 10px 10px 10px rgba(0, 0, 0, 0.2);
      min-height: calc(100vh - 60px);
    }

    .logo {
      margin-bottom: 43px;
      display: flex;
      justify-content: center;
    }

    h1 {
      font-size: 28px;
      font-weight: 600;
      color: var(--black);
      line-height: 1.1;
      margin-bottom: 29px;
      text-align: center;

      @media (max-width: 767.98px) {
        font-size: 22px;
      }
    }

    .label {
      margin-bottom: 12px;
      color: var(--dark-grey) !important;

      &.withLink {
        display: flex;
        align-items: center;
        justify-content: space-between;

        a {
          color: var(--dark-blue);
          text-decoration: none;
          margin-left: 7px;
        }
      }
    }

    .form-control {
      .MuiInputBase-input {
        border-radius: 8px;
        background: #ffffff;
        height: 48px;

        &:focus {
          border-color: #e0effe;
          box-shadow: 0px 0px 0px 2px inset #e0effe;

          & + .MuiInputAdornment-positionEnd {
            .MuiIconButton-edgeEnd {
              border: solid 3px #e0effe;
              border-left: none;
              padding-right: 12px;
            }
          }
        }
      }

      fieldset {
        display: none;
      }

      &.withAdornment {
        .MuiInputBase-input {
          border-right: none;
          border-top-right-radius: 0px;
          border-bottom-right-radius: 0px;
        }

        .MuiInputAdornment-positionEnd {
          margin-left: 0px;

          .MuiIconButton-edgeEnd {
            border: solid 1px var(--input-box-border);
            border-left: none;
            border-radius: 0px 8px 8px 0px;
            height: 48px;
            width: calc(100% + 3px);
            margin-left: -3px;
            z-index: 1;
            position: relative;
            background: white;
            padding-left: 14px;
            padding-right: 14px;
          }
        }
      }
    }

    .btn-orange {
      height: 48px !important;
      border-radius: 8px !important;
      margin: 24px 0 !important;
    }

    .regular-text {
      color: var(--gray-1);
      font-size: 16px;
      text-align: center;

      a {
        color: var(--dark-blue);
        text-decoration: none;
        margin-left: 7px;
      }
    }
  }
}

.additonal-data-filters {
  td {
    vertical-align: top;

    .autocomplete-pad-right {
      .MuiAutocomplete-input {
        padding-right: 55px !important;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;

        &::placeholder {
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          padding-right: 10px;
        }

        input:placeholder-shown {
          text-overflow: ellipsis;
        }
      }
    }
  }
}

.vertical-scroll {
  max-height: 100px;
  overflow-y: auto;

  .MuiChip-filled {
    font-size: 12px;
    margin-right: 6px !important;

    & + .MuiChip-filled {
      margin-left: 0px !important;
    }

    span {
      padding-left: 8px;
      padding-right: 8px;
    }
  }
}

.password-box {
  .MuiInputBase-fullWidth {
    padding-right: 0px;
  }
}

.break-columns-grid {
  padding-top: 0px !important;
}

.MuiAutocomplete-popper {
  .MuiMenuItem-gutters {
    padding-left: 6px;
    padding-right: 6px;
  }

  .MuiCheckbox-root {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .MuiAutocomplete-option {
    margin-left: 0px;
    padding-left: 16px;
  }

  .MuiCheckbox-indeterminate {
    color: var(--orange-60) !important;
  }

  .Mui-checked {
    color: var(--orange) !important;

    .MuiSvgIcon-colorPrimary {
      color: var(--orange) !important;
    }
  }
}

.idle-window-dialog {
  .MuiPaper-elevation {
    @media (min-width: 576px) {
      min-width: 380px;
    }
  }

  .idle-window {
    text-align: center;

    .warning-icon {
      width: 120px;
    }

    p {
      margin: 0 0 0;
    }
  }
}

.seprator {
  border-top: solid 1px #c1c1c1;
}

.no-italic {
  font-style: normal;
}

.datatable-parent-container {
  overflow: hidden;
}
.icon-pin-datagrid {
  position: absolute;
  top: 30px;
  right: 0px;
  z-index: 8;
  background: transparent;
  border: none;
  padding: 0;
  cursor: pointer;
  transform: rotate(-180deg);
  transition: 0.5s all ease-in;
}

.datatable-parent-header {
  border-left: solid 1px var(--table-border);
  border-right: solid 1px var(--table-border);
  border-top: solid 1px var(--table-border);
  border-bottom: solid 1px var(--table-border);
  margin-bottom: -1px;
  z-index: 1;
  position: relative;
  background-color: var(--table-header-bg);
  display: flex;
  padding-left: 80px;
  .cols {
    padding: 17px 8px;

    font-size: 15px;
    color: var(--black);
    font-weight: 700;
    position: relative;
    &:after {
      content: "";
      position: absolute;
      top: 0px;
      right: 0px;
      width: 1px;
      height: 1000%;
      background: var(--table-border);
      z-index: 9;
    }

    &.col-res {
      text-align: center;
    }
    &.col-last {
      flex: 1;
      &:after {
        display: none;
      }
    }
  }
}

.dataTable {
  &.hide-progress-icon {
    .MuiCircularProgress-root {
      display: none;
    }
  }
}

.zindex1200 {
  z-index: 1200;
}
.filter-autocomplete-zindex {
  z-index: calc(var(--z-index1200) + 3);
}

.container-cell {
  display: flex !important;
  flex-direction: column !important;
  flex-wrap: wrap !important;

  @media (min-width: 900px) {
    height: 630px;
  }

  .cell {
    @media (min-width: 900px) {
      width: 50%;
    }
  }

  .cell-1 {
    @media (min-width: 900px) {
      flex-basis: 100%;
    }
  }

  .cell-1 {
    order: 3;

    @media (min-width: 900px) {
      order: 1;
    }
  }

  .cell-2 {
    order: 1;

    @media (min-width: 900px) {
      order: 2;
    }
  }

  .cell-3 {
    order: 3;
  }
}

.download-sample-btn {
  display: flex;
  align-items: center;
  column-gap: 8px;
}

.download-svg-icon {
  width: 1em;
  height: 1em;
  display: inline-block;
  font-size: 1.2857142857142856rem;
  fill: currentColor;
  flex-shrink: 0;
}

.table-focused {
  .form-control-1,
  .form-control,
  .MuiSelect-select,
  .MuiNativeSelect-root {
    &:focus {
      box-shadow: var(--input-focus-shadow);
    }

    input,
    select {
      &:focus {
        box-shadow: var(--input-focus-shadow);
      }
    }
  }
}

.upload-icon-info {
  cursor: pointer;
  display: inline-block;
  position: absolute;
  top: -1px;
  right: -22px;

  svg {
    display: block;
  }
}

.custom-table {
  .align-center {
    display: flex;
    align-items: center;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .file-scroll {
    max-height: 130px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 12px;
  }

  .file-name-box {
    padding: 3px 0;
    border-radius: 0px;
    position: relative;

    & + .file-name-box {
      margin-top: 3px;

      &:before {
        content: "";
        height: 1px;
        position: absolute;
        top: -2px;
        left: 0;
        width: 100%;
        background: var(--table-border);
      }
    }
  }

  .row-hr {
    a {
      display: block;
      padding-bottom: 3px;
    }

    & + .row-hr {
      a {
        position: relative;
        padding-top: 3px;
        padding-bottom: 3px;

        &:before {
          content: "";
          position: absolute;
          top: 0px;
          left: -12px;
          width: calc(100% + 24px);
          height: 1px;
          background: var(--table-border);
        }
      }
    }
  }
}

// .inline-varialbes-list {
//   padding-left: 24px;
//   padding-right: 24px;
//   padding-bottom: 4px;
//   @media (max-width: 1199.98px) {
//     padding-left: 16px;
//     padding-right: 16px;
//   }
//   .variables-inner {
//     position: relative;
//     overflow: hidden;
//     .MuiGrid-item {
//       &:nth-child(3n) {
//         .columns {
//           //background:red;
//           &:before {
//             width: 600%;
//             height: 1px;
//             bottom: calc(-40px + 16px);
//             left: 50%;
//             right: inherit;
//             transform: translateX(-50%);
//             top: inherit;
//             @media (max-width: 1535.98px) {
//               bottom: calc(-28px + 16px);
//             }
//           }
//         }
//       }
//     }
//   }
//   .columns {
//     position: relative;
//     &:before {
//       content: "";
//       position: absolute;
//       top: 0;
//       width: 1px;
//       height: 100%;
//       background: var(--input-box-border);
//       right: -32px;
//       @media (max-width: 1535.98px) {
//         right: -24px;
//       }
//       @media (max-width: 1199.98px) {
//         right: -16px;
//       }
//     }
//   }
//   h3 {
//     margin: 0 0 16px;
//   }
//   h4 {
//     margin: 0 0 16px;
//     font-size: 18px;
//   }
//   ul {
//     padding: 0px;
//     margin: 0px;
//     margin-bottom: 24px;
//     position: relative;
//     &.no-bdr {
//       margin-bottom: 0px;
//       &:before {
//         display: none;
//       }
//     }
//     li {
//       list-style-type: none;
//       padding-bottom: 16px;
//       .name {
//         margin-bottom: 8px;
//         display: block;
//       }
//       .value {
//         flex: 1 0 auto;
//       }
//     }
//   }
// }

.column_statistics {
  max-height: 130px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--light-grey);
    border-radius: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--grey);
    border-radius: 10px;
  }
}

.inline-variables-table {
  width: 100%;
  border-top: solid 1px var(--table-border);
  border-right: solid 1px var(--table-border);
  border-collapse: collapse;

  tr {
    td {
      border-bottom: solid 1px var(--table-border);
      border-left: solid 1px var(--table-border);
      padding: 6px 12px;
      text-align: left;

      .inner-column {
        display: flex;
        align-items: center;
        justify-content: center;
        column-gap: 15px;

        &.flex-start {
          align-items: flex-start;
        }

        .label {
          flex: 0 0 120px;

          &.auto-width {
            flex: 0 0 auto;
          }

          &.pad-8 {
            padding-top: 8px;
          }
        }
      }
    }

    &:hover {
      td {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }
}

.file-headers-list {
  p {
    margin: 0 0 5px;
  }

  ul {
    margin: 0;
    padding: 0px 0 0 15px;

    li {
      list-style-type: disc;
    }
  }
}

.modal-dialog-1 {
  .MuiPaper-rounded {
    border-radius: 8px;
  }

  .dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    margin-bottom: 1.5rem;
    background: var(--card-bg);

    &:after {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 1px;
      background: var(--input-box-border);
      content: "";
      transform: translateX(-50%);
      left: 50%;
    }

    span {
      margin: 0;
      font-family: Inter, sans-serif;
      font-size: 20px;
      font-weight: 300;
      line-height: 1.167;
    }

    &.mb-0 {
      margin-bottom: 0px;
    }
  }

  .dialog-content {
    padding: 24px;
  }

  .dialog-footer {
    padding: 24px 24px 24px;
    position: relative;

    &:before {
      position: absolute;
      top: 0;
      width: 100%;
      height: 1px;
      background: var(--input-box-border);
      content: "";
      transform: translateX(-50%);
      left: 50%;
    }
  }

  .close-icon {
    background: var(--orange) url(../assets/svgs/icon-dialog-close-01.svg) 50%
      50% no-repeat;
    position: relative;
    border: none;
    cursor: pointer;
    display: block;
    padding: 10px;
    border-radius: 4px;
    background-size: 9px auto;
  }
}

%sql-editor {
  .sql-editor {
    margin: 10px auto;
    background-color: var(--white);
    border: solid 1px var(--table-border);
    border-radius: 4px;
    min-height: 30px;
    overflow: hidden;

    &.mb-0 {
      margin-bottom: 0px;
    }
    &.mt-0 {
      margin-top: 0px;
    }

    &.ace_focus {
      box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
    }

    .ace_gutter {
      //padding-top: 8px;
      background: var(--card-bg);
      color: var(--light-grey);

      .ace_gutter-active-line {
        background: transparent;
      }
    }

    .ace_scroller {
      //margin-top: 8px;
    }

    .ace_content {
      padding-top: 0px;
    }

    .ace_active-line {
      background: transparent;
    }

    .ace_scroller {
      background: var(--white);
    }

    .ace_hidden-cursors {
      .ace_cursor {
        opacity: 0;
      }
    }

    .ace_placeholder {
      opacity: 1;
      color: var(--light-grey);
    }
  }
}

%available-group {
  .avail-columns-group {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 5px;
    flex-wrap: wrap;
    max-height: 100px;
    overflow-y: auto;
    margin-bottom: 10px;

    .avail-columns {
      border: solid 1px var(--dark-blue);
      background: transparent;
      font-size: 14px;
      color: var(--dark-blue);
      font-weight: 500;
      padding: 6px;
      line-height: 1.1;
      border-radius: 4px;
      font-family: "Inter", Arial, Helvetica, sans-serif;
      cursor: pointer;
    }
  }
}

.main-dailog {
  font-family: "Inter", Arial, Helvetica, sans-serif;

  &.main-dailog-audit {
    .dailog-body {
      max-height: 70vh;
      padding-top: 0px;
      padding-left: 0px;
      padding-right: 0px;
    }
  }

  .dailog-body {
    max-height: 62vh;
    overflow-y: auto;
    overflow-x: hidden;
    padding-left: 15px;
    padding-right: 15px;
    margin-left: -15px;
    margin-right: -15px;
    padding-top: 24px;
    &.pb-24 {
      padding-bottom: 24px;
    }
  }

  .dailog-footer {
    position: relative;
    padding: 24px 0 0;

    &:before {
      content: "";
      position: absolute;
      top: 0;
      left: -24px;
      width: calc(100% + 48px);
      height: 1px;
      background: var(--input-box-border);
    }
  }

  .close-icon {
    background: var(--orange) url(../assets/svgs/icon-dialog-close-01.svg) 50%
      50% no-repeat;
    position: relative;
    border: none;
    cursor: pointer;
    display: block;
    padding: 10px;
    border-radius: 4px;
    background-size: 9px auto;
  }

  .seprator-line {
    position: relative;

    &.alternative {
      &:after {
        right: calc(60% - 50px);
        height: calc(100% + 5px);

        @media (max-width: 1199.98px) {
          right: calc(50% - 25px);
        }
      }
    }

    &:after {
      content: "";
      position: absolute;
      right: calc(50% - 25px);
      width: 1px;
      background: var(--input-box-border);
      top: -5px;
      height: calc(100% + 21px);

      @media (max-width: 899.98px) {
        display: none;
      }
    }
  }

  .modal-left-column {
    position: relative;

    &.negative-margin {
      //margin-top:-109px;
      //padding-top: 32px;
      &.negative-margin-1 {
        //margin-top:-192px;
      }

      @media (max-width: 599.98px) {
        margin-top: 0;
        padding-top: 0;
      }

      // &:after{
      //   top:-24px;
      //   height:calc(100% + 24px);
      //   @media(max-width:899.98px){
      //     display: none;
      //   }
      // }
    }

    // &:after{
    //   content:"";
    //   position: absolute;
    //   top:-24px;
    //   right:-25px;
    //   width:1px;
    //   height:calc(100% + 24px);
    //   background:var(--input-box-border);
    // }
  }

  .dailog-header {
    margin-left: -24px;
    margin-right: -24px;
    margin-top: -24px;
    padding: 16px 24px;
    width: auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--card-bg);
    position: relative;

    &:after {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 1px;
      background: var(--input-box-border);
      content: "";
      transform: translateX(-50%);
      left: 50%;
    }

    label {
      margin: 0;
      padding: 0;
      font-weight: 300;
      color: var(--black);
      font-size: 20px;
    }
  }

  .add-column-box {
    position: relative;
    padding-bottom: 24px;
    margin-bottom: 24px;

    &.alternative {
      //border:solid 1px red;
      &:first-child {
        margin-top: 30px;

        &:after {
          content: "";
          position: absolute;
          top: 0;
          left: 15px;
          width: calc(100% + 9px);
          height: 1px;
          background: var(--input-box-border);
        }
      }

      &:before {
        width: calc(100% + 9px);
      }
    }

    &:before {
      content: "";
      position: absolute;
      bottom: 0;
      left: 15px;
      width: calc(100% + 8px);
      height: 1px;
      background: var(--input-box-border);
    }
  }

  .dailog-body {
    label {
      font-size: 14px;
      color: var(--dark-grey);
    }

    input[type]:not([type="checkbox"]) {
      border: solid 1px var(--input-box-border);
      height: 36px;
      color: var(--dark-grey);
      font-size: 15px;

      &::placeholder {
        color: var(--light-grey);
      }
    }

    .editor-textbox {
      input[type]:not([type="checkbox"]) {
        color: #2e384d;
      }
    }

    .sql-query-box {
      .queryBuilder {
        max-height: 300px;
        overflow-y: auto;
      }
    }

    .sub-title,
    h3 {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 16px;
    }

    .sql-textarea-box {
      h3 {
        font-size: 14px;
        color: var(--dark-grey);
        margin: 0;
        margin-bottom: 16px;
        font-weight: 600;

        &.mb-8 {
          margin-bottom: 8px;
        }
      }

      @extend %sql-editor;

      .available-box {
        box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.3);
        padding: 6px 10px 6px 10px;
        border-radius: 4px;

        &.box-alt {
          padding-bottom: 1px;
        }

        .btn-orange {
          height: auto !important;
          min-width: inherit !important;

          &.btn-sm {
            padding: 5px 6px 4px 6px !important;
          }
        }

        .btn-dark {
          background: var(--dark-blue) !important;
        }
      }

      .add-variable-form {
        padding-top: 16px;
      }

      @extend %available-group;
    }

    .sql-result-box {
      margin-bottom: 20px;
      &.mb-0 {
        margin-bottom: 0px;
      }

      h4 {
        font-size: 14px;
        color: var(--dark-grey);
        margin: 0 0 16px;
        font-weight: 600;
      }

      .result-bg {
        background: var(--chip-bg);
        border: solid 1px var(--input-box-border);
        padding: 20px 30px;
        border-radius: 4px;

        font-size: 14px;
        color: var(--black);
        line-height: 24px;
      }
    }

    .ruleGroup {
      background: transparent;
      border-color: transparent;
      padding: 0;

      input[type="checkbox"] {
        margin-left: 0;
      }

      .ruleGroup-header {
        column-gap: 16px;
        flex-wrap: wrap;
      }

      .ruleGroup-notToggle {
        input[type="checkbox"] {
          width: 18px;
          height: 18px;
          margin-right: 8px;
          vertical-align: bottom;
        }
      }

      .ruleGroup-addRule,
      .ruleGroup-addGroup,
      .ruleGroup-lock {
        border: solid 1px var(--orange);
        background: transparent;
        font-size: 16px;
        border-radius: 4px;
        padding: 8px 16px;
        color: var(--orange);
        cursor: pointer;
        background: white;
        font-weight: 500;
      }

      .ruleGroup-addRule {
        border-color: var(--dark-blue);
        color: var(--dark-blue);
      }

      .ruleGroup-addGroup {
        background-color: var(--dark-blue);
        border-color: var(--dark-blue);
        color: #ffffff;
      }

      .ruleGroup-lock {
        padding: 6px 6px 7px 6px;
        background: transparent;
        border-color: transparent;
      }

      .ruleGroup-lock {
        font-size: 0px;
        color: transparent;
        background: url(../assets/svgs/icon-lock-black.svg) 50% 50% no-repeat;
        padding: 16px 20px;
      }
    }
  }
}

.available-box {
  box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.3);
  padding: 6px 10px 6px 10px;
  border-radius: 4px;

  .flex-props {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    column-gap: 16px;
  }

  h3 {
    font-size: 14px;
    color: var(--dark-grey);
    margin: 0 0 8px;
  }

  @extend %available-group;
}

.ace-editor {
  @extend %sql-editor;
}

.params-box {
  .avail-columns-group {
    margin-bottom: 0;
  }

  .btn-orange {
    &.plus-btn-sm {
      padding: 2px 2px !important;
      height: 23px !important;
    }
  }

  .avail-columns-group {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 5px;
    flex-wrap: wrap;
    max-height: 100px;
    overflow-y: auto;

    .avail-columns {
      border: solid 1px var(--dark-blue);
      cursor: pointer;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      button {
        background: transparent;
        font-size: 14px;
        color: var(--dark-blue);
        font-weight: 500;
        padding: 6px;
        line-height: 1.1;
        border: none;

        & + button {
          border-left: solid 1px var(--dark-blue);
          border-radius: 0;
        }

        img {
          width: 20px;
        }
      }
    }
  }

  .add-variable-form {
    border: solid 1px var(--table-border);
    margin: 8px 0;
    padding-bottom: 15px;
    border-radius: 4px;
    width: auto;
  }

  .collapse-no-bdr {
    &:after {
      display: none;
    }

    :after {
      display: none;
    }
  }
}

.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
}

.btn-new-window {
  border: none;
  display: flex;
  align-items: center;
  column-gap: 4px;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }

  svg {
    width: 16px;
  }
}

.codeMarker {
  background: #fff677;
  position: absolute;
  z-index: 20;
}

.mui-stepper {
  border: solid 1px var(--input-box-border);
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  overflow: hidden;

  .MuiStep-root {
    padding-left: 5px;
    padding-right: 5px;
  }

  .MuiStepper-root {
    background: var(--card-bg);
    padding: 34px 18px;
    border-bottom: solid 1px var(--input-box-border);

    p {
      font-size: 15px;
      font-weight: 500;
      color: var(--dark-grey);
    }

    .MuiTypography-caption {
      font-size: 12px;
      font-weight: 400;
      color: #888888;
    }

    &.header-stepper {
      .MuiStep-root {
        .MuiStepLabel-root {
          .Mui-completed {
            .MuiSvgIcon-root {
              color: var(--success-bg-1);
            }
          }
        }

        .MuiStepLabel-iconContainer {
          font-size: 16px;
          font-weight: 500;
          padding-right: 14px;

          .MuiStepIcon-text {
            fill: var(--white);
            font-size: 14px;
          }

          .MuiSvgIcon-root {
            color: var(--gray-1);
            width: 28px;
            height: 28px;

            &.Mui-active {
              color: var(--orange);
            }
          }
        }

        &.active-tab {
          & + .MuiStepConnector-root {
            .MuiStepConnector-line {
              background-image: linear-gradient(
                to right,
                var(--orange) 50%,
                rgba(255, 255, 255, 0) 0%
              );
            }
          }
        }

        &.Mui-completed {
          & + .MuiStepConnector-root {
            .MuiStepConnector-line {
              background-image: linear-gradient(
                to right,
                var(--success-bg-1) 50%,
                rgba(255, 255, 255, 0) 0%
              );
            }
          }
        }
      }
    }

    &.header-stepper {
      .MuiStepConnector-root {
        //border: solid 1px red;
        .MuiStepConnector-line {
          background-image: linear-gradient(
            to right,
            var(--light-grey) 50%,
            rgba(255, 255, 255, 0) 0%
          );
          background-position: bottom;
          background-size: 8px 2px;
          background-repeat: repeat-x;
          border: none;
          width: calc(100% - 32px);
          height: 2px;
          margin-right: 16px;
          margin-left: 16px;
          position: relative;

          &:before {
            position: absolute;
            top: 0;
            left: 0px;
            width: 2px;
            height: 2px;
            background: var(--white);
            content: "";
          }

          &:after {
            position: absolute;
            top: 0;
            right: 0px;
            width: 2px;
            height: 2px;
            background: var(--white);
            content: "";
          }
        }
      }
    }
  }

  .stepper-body {
    padding: 20px 30px;

    .svg-input-opener {
      cursor: pointer;
      text-align: center;
    }

    .file-upload-box {
      border: 1px dashed rgba(56, 78, 183, 0.3);
      border-radius: 4px;
      padding: 30px;
      background: var(--card-bg);
      max-width: 445px;
      margin: 25px auto;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      h3 {
        font-size: 16px;
        font-weight: 700;
        color: var(--orange);
        text-decoration: underline;
        margin: 0 0 10px;
      }

      p {
        font-size: 12px;
        font-weight: 400;
        color: var(--light-grey);
        margin: 0 0 10px;
      }
    }
  }

  .stepper-footer {
    border-top: solid 1px var(--input-box-border);
    padding: 20px 30px;
    display: flex;
    justify-content: flex-end;
    column-gap: 10px;

    &.between {
      display: flex;
      justify-content: space-between;
    }
  }

  .uploading-files {
    position: relative;

    h4 {
      font-size: 14px;
      color: var(--grey);
      font-weight: 700;
      margin: 0 0 13px;
    }

    .file-name {
      border: solid 1px var(--input-box-border);
      border-radius: 4px;
      padding: 8px 16px;
      font-size: 12px;
      color: var(--black);
      position: relative;

      .icon-close {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 16px;
        background-color: var(--gray-1);
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;

        svg {
          color: var(--white);
          width: 14px;
        }
      }
    }

    .MuiLinearProgress-root {
      position: absolute;
      bottom: -1px;
      left: 1px;
      width: calc(100% - 2px);
      background-color: transparent;
      height: 3px;

      .MuiLinearProgress-bar {
        background-color: var(--orange);
        border-radius: 8px;
      }
    }
  }

  .last-step {
    padding: 0px;
    border: none;
    background: none;

    .MuiStepConnector-line {
      border: none;
      position: relative;

      &:before {
        content: "";
        border: dashed 1px var(--gray-1);
        width: 1px;
        height: calc(100% + 15px);
        position: absolute;
        top: -17px;
        left: -2px;
      }
    }

    .MuiStepLabel-root {
      padding: 0;
    }

    svg {
      path {
        fill: var(--gray-1);
      }
    }

    .MuiStep-vertical {
      &.status-success {
        .MuiTypography-body2 {
          color: var(--success-bg-1);
          background-image: url(../assets/svgs/icon-check-circle-green.svg);
        }

        svg {
          path {
            fill: var(--success-bg-1);
          }
        }
      }

      &.status-failed {
        .MuiTypography-body2 {
          color: var(--badge-danger-bg);
          background-image: url(../assets/svgs/icon-warning-red.svg);
        }

        svg {
          path {
            fill: var(--badge-danger-bg);
          }
        }
      }

      &.status-not_proceed {
        //border: solid 1px yellow;
      }

      .MuiTypography-body1 {
        font-size: 18px;
        font-weight: 300;
        margin-bottom: 2px;
        color: var(--black);
      }

      .MuiTypography-body2 {
        font-size: 14px;
        font-weight: 400;
        font-style: italic;
        margin-bottom: 0px;
        padding-left: 18px;
        background-position: 0 50%;
        background-repeat: no-repeat;
        display: flex;
        align-items: center;
        column-gap: 9px;
      }
    }

    .MuiStepLabel-root {
      align-items: flex-start;

      .MuiStepLabel-iconContainer {
        padding-top: 3px;
      }
    }
  }

  .icon-refresh {
    border: none;
    background: none;
    padding: 0;
    cursor: pointer;

    svg {
      path {
        fill: var(--white) !important;
      }
    }
  }

  .MuiStepLabel-label {
    display: flex;
    align-items: center;
    column-gap: 30px;

    .status-msgs {
      flex: 0 0 40%;
    }

    .buttons-group {
      display: flex;
      align-items: center;
      column-gap: 20px;

      .stepper-btn {
        border: solid 1px var(--orange);
        border-radius: 4px;
        width: 38px;
        height: 38px;
        background: transparent;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &.btn-db {
          border-color: var(--orange);

          svg {
            path {
              fill: var(--orange);
            }
          }
        }

        &.btn-bin {
          border-color: var(--light-grey);

          svg {
            path {
              fill: transparent;
              stroke: var(--light-grey);
            }
          }
        }

        &.btn-import {
          border-color: var(--dark-grey);

          svg {
            path {
              fill: transparent;
              stroke: var(--dark-grey);
            }
          }
        }

        &.btn-plus {
          border-color: var(--orange);

          svg {
            path {
              fill: var(--orange);
            }
          }
        }
      }
    }
  }

  .timeline {
    g {
      clippath: border-box;
    }

    & > .MuiTimeline-root {
      & > .MuiTimelineItem-root {
        &.status-success {
          & > .MuiTimelineSeparator-root {
            svg {
              path {
                fill: var(--success-bg-1);
              }
            }
          }
        }

        &.status-failed {
          & > .MuiTimelineSeparator-root {
            svg {
              path {
                fill: var(--badge-danger-bg);
              }
            }
          }
        }
      }
    }

    .no-data-avai {
      box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.2);
      display: flex;
      align-items: center;
      column-gap: 8px;
      border-radius: 4px;
      padding: 12px 15px;

      svg {
        color: var(--orange);
      }
    }

    & .MuiTimeline-root {
      padding: 0px;
      margin: 0px;

      & .MuiTimelineItem-root {
        min-height: inherit;

        &:before {
          display: none;
        }

        .MuiTimelineConnector-root {
          background-color: transparent;
          border-left: dashed 2px var(--gray-1);
          margin-top: 5px;
        }
      }
    }

    .MuiTypography-body1 {
      padding-left: 15px;
    }

    .MuiTimelineSeparator-root {
      padding-top: 8px;
    }

    .nested-child {
      & > .MuiTimeline-root {
        & > .MuiTimelineItem-root {
          &:last-child {
            .child-item {
              margin-bottom: 0px;
            }
          }

          &.status-success {
            & > .MuiTimelineSeparator-root {
              svg {
                path {
                  fill: var(--success-bg-1);
                }
              }
            }
          }

          &.status-failed {
            & > .MuiTimelineSeparator-root {
              svg {
                path {
                  fill: var(--badge-danger-bg);
                }
              }
            }
          }
        }
      }
    }

    .child-item {
      display: flex;
      align-items: center;
      margin-bottom: 30px;

      &.mb-0 {
        margin-bottom: 0px;
      }

      .column-first {
        flex: 0 0 60%;
      }

      .column-second {
        display: flex;
        align-items: center;
        column-gap: 16px;

        button {
          border: solid 1px var(--orange);
          border-color: transparent;
          padding: 0;
          border-radius: 4px;
          width: 38px;
          height: 38px;
          background: transparent;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          position: relative;

          .loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: solid 1px var(--gray-1);
            border-radius: 4px;
            width: 38px;
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;

            .MuiCircularProgress-indeterminate {
              max-height: 28px;
              max-width: 28px;
            }

            circle {
              stroke: var(--gray-1);
            }
          }

          &.btn-db {
            border-color: transparent;

            svg {
              path {
                //fill: var(--orange);
              }
            }
          }

          &.btn-bin {
            border-color: transparent;

            svg {
              path {
                fill: transparent;
                //stroke: var(--light-grey);
              }
            }
          }

          &.btn-import {
            border-color: var(--dark-grey);

            svg {
              path {
                fill: transparent;
                stroke: var(--dark-grey);
              }
            }
          }

          &.btn-plus {
            border-color: var(--orange);

            svg {
              path {
                fill: var(--orange);
              }
            }
          }
        }
      }
    }
  }
}

.loaderContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loaderImg {
  max-width: 50px;
  max-height: 50px;
}

.loader {
  display: flex;
  justify-content: center;
  margin-top: 10px;
  /* Adjust as needed */
}

.dot {
  width: 10px;
  /* Adjust size as needed */
  height: 10px;
  margin: 0 4px;
  border-radius: 50%;
  background-color: var(--dark-grey);
  animation: bounce 1.4s infinite both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1);
  }
}

.import-navigate-entity {
  cursor: pointer;

  &:hover {
    color: var(--orange);
  }
}

.incident-box {
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.4);
  transition: all 0.5s ease-in;
  &.box-shadow10 {
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.4);
  }
  &.bg-triggered {
    .MuiPaper-elevation {
      border-color: #e84336;
      .heading {
        color: #e84336;
      }
    }
  }
  &.bg-acknowledged {
    .MuiPaper-elevation {
      border-color: #ffbf00;
      .heading {
        color: #ffbf00;
      }
    }
  }

  .rule-domain-chart-box {
    border: none;
    padding: 0px;
    h3 {
      &:before {
        width: calc(100% + 32px);
        left: -16px;
      }
    }
  }

  .incident-btns {
    display: flex;
    align-items: center;
    column-gap: 4px;
    padding: 2px 6px !important;
    &.acknowledge {
      border-color: #ffbf00 !important;
      color: #ffbf00 !important;
    }
    &.get-resolve {
      border-color: #196bb4 !important;
      color: #196bb4 !important;
    }
    &.resolved {
      border-color: #74b856 !important;
      color: #74b856 !important;
    }
    svg {
      max-width: 28px;
      max-height: 28px;
    }
  }

  .incidents {
    margin: 0px;
    padding: 0px;
    display: flex;
    align-items: center;
    column-gap: 15px;
    justify-content: space-between;
    .cols {
      list-style-type: none;
      &.col-gap8 {
        column-gap: 8px;
        display: flex;
      }
    }
    .btn-sm {
      svg {
        max-height: 27px;
        max-width: 27px;
      }
    }
  }

  .comment-section {
    margin-top: 16px;
    .mb-1 {
      margin-bottom: 0.25rem;
      display: block;
    }
    .mb-2 {
      margin-bottom: 0.5rem;
      display: block;
    }
    h3 {
      font-size: 16px;
      line-height: 1.3;
      margin-bottom: 15px;
      span.normal {
        font-size: 14px;
        line-height: 1.2;
        font-weight: 400;
      }
    }
    .comment-box {
      margin-bottom: 16px;
    }
    .textarea {
      max-height: 80px;
      overflow-y: auto !important;
    }

    ul {
      margin: 0px;
      padding: 0px;
      max-height: 300px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: var(--dark-blue50);
        border-radius: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--dark-blue);
        border-radius: 10px;
      }
      li {
        list-style-type: none;
        margin: 0 0 0px;
        box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.3);
        border-radius: 4px;
        padding: 6px 12px;
        border: solid 1px var(--input-box-border);
        font-size: 13px;
        & + li {
          margin-top: 8px;
        }
      }
    }
  }
  .required {
    color: #d32f2f;
    margin-left: 2px;
  }
}

.invisible-list-url {
  padding: 0px;
  margin: 0px;
  li {
    display: block;
    list-style-type: none;
    display: flex;
    align-items: center;
    column-gap: 8px;
    row-gap: 4px;
    margin-bottom: 8px;
    &.icon-stroke {
      svg {
        width: 17px;
        height: 17px;
        path {
          stroke: var(--black);
        }
      }
    }

    &.import {
      svg {
        width: 17px;
        height: 17px;
      }
    }

    a {
      color: var(--black);
      text-decoration: none;
    }
    svg {
      color: var(--dark-blue);
      width: 21px;
      height: 21px;
    }
    &.has-child {
      display: block;
      cursor: pointer;
      em {
        display: inline-flex;
        align-items: center;
        font-style: normal;
        position: relative;
        column-gap: 8px;
        color: var(--black);
        .arrow-icon {
          position: absolute;
          top: 0px;
          right: -14px;
          transform: rotate(45deg);
          transition: all 0.1s ease-in;
          svg {
            width: 7px;
            height: 7px;
          }
        }
      }
      &.child-open {
        .arrow-icon {
          transform: rotate(135deg);
        }
      }
      ul {
        margin: 0px;
        padding: 0px;
        padding-left: 18px;
        height: 0px;
        opacity: 0;
        transition: all 0.2s ease-in;
        &.opened {
          height: auto;
          opacity: 1;
        }
        li {
          margin-bottom: 0;
          padding-left: 0px;
          padding-top: 4px;
          padding-bottom: 4px;
          svg {
            color: var(--light-grey);
            width: 13px;
            height: 13px;
          }
        }
      }
    }
  }
}

.data-grid-cell {
  line-height: normal;
  padding: 10px;
  white-space: normal;
  word-wrap: break-word;
  //align-self: flex-start;
}
.ul-inside-grid {
  padding: 0px;
  margin: 0px;
  margin-left: 10px;
  li {
    word-break: break-all;
    list-style-type: square;
    & + li {
      margin-top: 5px;
    }
  }
}
.button-tree-panel {
  position: relative;
  .btn-parent {
    background: var(--dark-blue);
    color: var(--white);
    text-transform: capitalize;
    box-shadow: none;
    border-radius: 4px;
    min-height: 36px;
    font-size: 16px;
    font-weight: 400;
    &:hover {
      background: var(--dark-blue);
      box-shadow: none;
    }
  }
  .child-toggle {
    position: absolute;
    top: 100%;
    z-index: 9;
    right: 0px;
    background: var(--white);
    padding: 15px;
    max-height: 200px;
    box-shadow: 0px 0px 9px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    .nested-child-group {
      padding-left: 28px;
      padding-top: 10px;
      position: relative;
      &:before {
        content: "";
        width: 24px;
        height: 16px;
        border-left: solid 1px #cecece;
        border-bottom: solid 1px #cecece;
        border-bottom-left-radius: 4px;
        position: absolute;
        left: 0px;
        top: 16px;
      }
    }
    .btn-child {
      border: solid 1px var(--dark-blue);
      color: var(--dark-blue);
      box-shadow: none;
      background-color: transparent;
      text-transform: capitalize;
      font-weight: 500;
      font-size: 16px;
      padding-top: 2px;
      padding-bottom: 2px;
    }
  }
}
.checkbox-result-column {
  .MuiDataGrid-cellCheckbox {
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
  .MuiDataGrid-columnHeaderCheckbox {
    padding-left: 10px !important;
  }
}
.custom-chip {
  background: var(--chip-bg);
  border-radius: 4px;
  padding-left: 8px;
  padding-right: 8px;
  height: 36px;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  justify-content: center;
  border: solid 1px rgba(64, 114, 238, 0.3);
  &.size-md {
    height: 32px;
  }
  svg {
    margin-right: 0px;
    width: 24px;
    path {
      stroke: rgba(0, 0, 0, 0.8);
    }
  }
}
.accordion-panel {
  .execution-secondary-resource {
    position: relative;
    margin-left: 48px !important;
    &:before {
      content: "";
      position: absolute;
      top: 50%;
      left: -32px;
      width: 2.5px;
      height: calc(100% - 4px);
      transform: translateY(-50%);
      background: var(--orange);
      display: block !important;
      opacity: 1 !important;
    }
    &:after {
      content: "";
      position: absolute;
      top: 50%;
      left: -32px;
      width: 28px;
      height: 2px;
      transform: translateY(-50%);
      background: var(--orange);
      display: block !important;
    }
  }
}
@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes fadeSlideOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-100%);
  }
}
.fadeSlideIn {
  animation: fadeSlideIn 0.6s forwards;
}
.fadeSlideOut {
  animation: fadeSlideOut 0.3s forwards;
}
.collapse-no-bdr {
  .MuiCollapse-vertical,
  .MuiCollapse-wrapperInner,
  .MuiCollapse-wrapper {
    &:after {
      display: none;
    }
  }
}

.MuiCollapse-vertical {
  &.no-border {
    &:after {
      display: none;
    }
    .MuiCollapse-vertical {
      &:after {
        display: none;
      }
    }
  }
}

.tabs-with-butttons {
  border-top: solid 1px var(--table-border);
  border-left: solid 1px var(--table-border);
  border-right: solid 1px var(--table-border);
  .mui-tabs {
    border: none;
    &.alternative-1 {
      border: none;
    }
  }
  .buttons-group {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 24px;
    gap: 12px;
  }
}

.incident-report-container {
  position: relative;
  .MuiDataGrid-columnHeader {
    padding-top: 0px !important;
  }

  .MuiDataGrid-columnHeadersInner {
    background: white;
  }

  .incident-filter-icon {
    position: absolute;
    top: 17px;
    left: 0px;
    z-index: 9;
  }
  .list-title {
    padding-left: 15px;
  }

  .MuiDataGrid-row {
    .no-padding-cell {
      padding-left: 0px !important;
      padding-right: 0px !important;
    }
  }

  .MuiDataGrid-columnHeaders {
    .MuiDataGrid-columnHeader {
      padding-left: 0px;
      padding-right: 0px;
      padding-bottom: 0px;

      &:first-child {
        padding-left: 0px !important;
        padding-right: 0px !important;
      }
      &:nth-child(2) {
        .textbox-group,
        .header-name {
          padding-left: 24px !important;
        }
      }
      &:last-child {
        padding-left: 0px !important;
        padding-right: 0px !important;

        .textbox-group,
        .header-name {
          padding-right: 24px !important;
        }
      }
    }
  }

  .header-name {
    padding-left: 8px;
    padding-right: 8px;
    padding-top: 16px;
    padding-bottom: 16px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    background: var(--table-header-bg);
  }
  .MuiDataGrid-columnSeparator {
    display: none !important;
  }
  .textbox-group-wrapper {
    height: auto;
    transition: 0.5s all ease-in;
    opacity: 1;
    padding-top: 16px;
    padding-bottom: 16px;

    &.hide {
      height: 0px;
      opacity: 0;
      display: none;
    }
  }

  .MuiDataGrid-columnHeaderTitleContainerContent {
    flex: 1;
    .MuiBox-root {
      flex: 1;
    }
  }
}
.domain_name_error {
  .MuiFormHelperText-root {
    color: #d32f2f;
  }
}

.incident-resources {
  padding: 24px;
  .incident-list {
    margin: 0 0 24px;
  }
  h3 {
    font-size: 20px;
    color: var(--dark-blue);
    margin: 0 0 12px;
    text-transform: uppercase;
  }
  .table1 {
    border-top: solid 1px var(--input-box-border);
    border-left: solid 1px var(--input-box-border);
    border-collapse: collapse;
    width: 100%;
    tr:nth-child(even) {
      td {
        background: var(--table-row-alt-bg);
      }
    }
    th,
    td {
      border-bottom: solid 1px var(--input-box-border);
      border-right: solid 1px var(--input-box-border);
      padding: 5px 5px;
      background: var(--white);
    }
    td {
      vertical-align: top;
    }
    .width-180 {
      width: 180px;
    }
  }
}
.re-run-header {
  background-color: var(--card-bg);
  padding: 12px 8px;
  border-radius: 4px 4px 4px 4px;
  display: flex;
  align-items: center;
  gap: 16px;
  border-left: solid 2px #cecece;

  h2 {
    margin: 0;
    font-size: 16px;
  }
  .re-run-textfield {
    min-width: 200px;
    max-width: 40%;
    flex: 1;
  }
}

.ghost-datatable {
  border: solid 1px var(--input-box-border);
  width: 100%;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  .circle {
    position: relative;
    z-index: 9;
    color: rgba(0, 0, 0, 0.5);
  }
  &::before {
    content: "";
    position: absolute;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
    //background: rgba(0, 0, 0, 0.5);
    z-index: 1;
  }
}

.burger {
  width: 32px;
  height: 32px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  gap: 2px;
  padding: 7px 6px !important;
  &:before {
    display: none;
  }
  .bar {
    width: 100%;
    height: 2px;
    background-color: #fff;
    transition: all 0.3s ease;
  }
  .bar1 {
    width: 10px;
  }
  .bar3 {
    width: 10px;
  }
  &.active {
    .bar1 {
      transform: rotate(45deg) translateY(1px);
    }
    .bar2 {
      opacity: 0;
    }
    .bar3 {
      transform: rotate(-45deg) translateY(-1px);
    }
  }
}
.run-name-update {
  display: flex;
  gap: 0px;
  justify-content: center;
  align-items: flex-start;
  .form-control-autocomplete {
    .MuiInputBase-formControl {
      .MuiInputBase-input {
        border-top-right-radius: 0px;
        border-bottom-right-radius: 0px;
      }
    }
  }
  .btn-group {
    .btn-orange {
      padding: 8px !important;
      min-width: inherit;
      border-radius: 0px !important;
      &.btn-dark {
        &:last-child {
          border-top-right-radius: 4px !important;
          border-bottom-right-radius: 4px !important;
          border-left: solid 1px var(--white) !important;
        }
      }
    }
  }
}
.datatable-footer {
  font-size: 14px;
  padding: 16px 40px;
  display: flex;
  align-items: center;
  column-gap: 6px;
}
.limit-chracters {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap !important;
  max-width: 95%;
}
.rs-query-tabs {
  min-height: 28px !important;
  .MuiTab-root {
    padding: 0px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background: var(--dark-grey);
    text-transform: none;
    padding-left: 6px;
    padding-right: 6px;
    min-height: 28px;
    color: var(--white);
    & + .MuiTab-root {
      margin-left: 4px;
    }
    &.Mui-selected {
      color: var(--white);
    }
  }
}
.rs-query-tabs-content {
  border: solid 1px var(--input-box-border);
  padding: 8px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  .MuiButtonBase-root {
    height: 28px;
  }
}

@import "autoXLR8";
