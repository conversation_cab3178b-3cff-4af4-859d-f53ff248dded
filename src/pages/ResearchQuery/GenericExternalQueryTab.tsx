import {
  Autocomplete,
  Box,
  Button,
  Grid,
  IconButton,
  ListItemText,
  TextField,
} from "@mui/material";
import React, {
  Dispatch,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import {
  IResearchQuery,
  IResearchQueryDetail,
} from "../../types/researchQuery";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import {
  extractVariables,
  replaceQueryToDomainMapped,
} from "../../services/utils";
import * as Yup from "yup";
import {
  genericExternalQuerySchema,
  GenericResourceFormSchema,
} from "../../schemas";
import GenericQueryBuilderDialog from "./GenericQueryBuilderDialog";

interface GenericResourceQueryTabProps {
  researchQueryData: IResearchQueryDetail;
  setResearchQueryData: any;

  handleTransition: any;
  columns: any;
  setColumns: Dispatch<SetStateAction<any>>;
  isEditQuery: any;
  setIsEditQuery: any;
  editFormData: any;
  setQueryType: any;
  setEditFormData: any;
  setIsAddQueryBtnEnabled: any;
  handleSaveResearchQuery?: (researchQuery: IResearchQuery[]) => void;
  isFromExecution?: boolean;
  setErrors?: any;
}

const GenericExternalQueryTab = ({
  handleSaveResearchQuery,
  handleTransition,
  researchQueryData,
  setResearchQueryData,
  columns,
  setColumns,
  isEditQuery,
  setIsEditQuery,
  editFormData,
  setQueryType,
  setEditFormData,
  setIsAddQueryBtnEnabled,
  isFromExecution = false,
  setErrors,
}: GenericResourceQueryTabProps) => {
  const [openQueryDialogIndex, setOpenQueryDialogIndex] = useState<
    number | null
  >(null);
  const [formData, setFormData] = useState<any>({});
  const [queryError, setQueryError] = useState<{ [key: string]: string }>({});
  const [mergedQueryColumns, setMergedQueryColumns] = useState<any>();
  const [variables, setVariables] = useState<any>([]);
  const [connectionKeys, setConnectionKeys] = useState<any[]>([]);
  const [linkedServices, setLinkedServices] = useState<any[]>([]);
  const [linkedServicesValue, setLinkedServicesValue] = useState<any>(null);
  const [connectionKeysValue, setConnectionKeysValue] = useState<any>(null);

  const {
    linkedServicesData,
    fetchedConnectionKeys,
    editorRef,
    queryBuilderTempValue,
    setQueryBuilderTempValue,
    globalVariables,
    tempGlobalVariables,
    setTempGlobalVariables,
    setGlobalVariables,
  } = useRuleResourceContext();
  useEffect(() => {
    if (linkedServicesData) {
      setLinkedServices(
        linkedServicesData.filter((s: any) => s.type === "sql")
      );
    }
  }, [linkedServicesData]);

  useEffect(() => {
    const queryVariables: any = extractVariables(queryBuilderTempValue);
    setVariables(queryVariables ?? []);
  }, [queryBuilderTempValue]);

  useEffect(() => {
    if (variables?.length > 0 && globalVariables) {
      const globalVars: any = {};
      variables.forEach((variable: any) => {
        if (globalVariables[variable] !== undefined) {
          globalVars[variable] = globalVariables[variable];
        } else if (tempGlobalVariables[variable] !== undefined) {
          globalVars[variable] = tempGlobalVariables[variable];
        } else {
          globalVars[variable] = "";
        }
      });
      // Compare the current tempLocalVariables with the new ones to avoid unnecessary updates
      if (JSON.stringify(tempGlobalVariables) !== JSON.stringify(globalVars)) {
        // setTempLocalVariables(globalVars);
        setTempGlobalVariables(globalVars);
      }
    } else {
      setTempGlobalVariables({});
    }
  }, [variables]);

  const handleChangeQuery = (event: any) => {
    const { name, value } = event.target;
    setFormData((prev: any) => ({
      ...prev,
      [name]: value,
    }));
    validateField(name, value);
  };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await genericExternalQuerySchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        [name]: undefined,
      }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };
  const handleSaveQuery = async () => {
    try {
      const researchQuery: any = [...researchQueryData.research_queries];

      const updatedFormData = {
        ...formData,
        query: queryBuilderTempValue,
      };

      try {
        await GenericResourceFormSchema.validate(researchQueryData, {
          abortEarly: false,
        });
      } catch (formErrors: any) {
        const newErrors: { [key: string]: string } = {};
        if (formErrors.inner) {
          formErrors.inner.forEach(
            (error: { path: string | number; message: string }) => {
              newErrors[error.path] = error.message;
            }
          );
        }
        setErrors((prevErrors: any) => ({
          ...prevErrors,
          ...newErrors,
        }));
        return;
      }

      await genericExternalQuerySchema.validate(updatedFormData, {
        abortEarly: false,
      });

      // Variable validations
      const variableSchema: any = {};
      Object.keys(tempGlobalVariables).forEach((key: any) => {
        if (tempGlobalVariables[key] === "") {
          variableSchema[key] = Yup.string().required(`Please enter ${key}`);
          setQueryError((prevErrors) => ({
            ...prevErrors,
            [key]: `Please enter ${key}`,
          }));
        }
      });

      const resourceQueryVariableSchema = Yup.object().shape(variableSchema);
      await resourceQueryVariableSchema.validate(tempGlobalVariables, {
        abortEarly: false,
      });

      setGlobalVariables((prev: any) => ({
        ...Object.keys(tempGlobalVariables).reduce(
          (acc, key) => ({ ...acc, [key]: tempGlobalVariables[key] }),
          prev
        ),
      }));

      const updatedQuery: string = replaceQueryToDomainMapped(
        queryBuilderTempValue,
        columns?.missingColumns
      );

      if (isEditQuery) {
        const index = researchQuery.findIndex(
          (item: any) => item.id === formData.id
        );

        if (index !== -1) {
          researchQuery[index] = {
            id: formData.id,
            name: formData.name,
            query: queryBuilderTempValue,
            updatedQuery: updatedQuery,
            source: [
              {
                type: "External",
                linked_service_id: formData?.linked_service?.id,
                linked_service_code: formData?.linked_service?.code,
                connection_key_id: formData?.connection_key?.id,
                connection_key_code: formData?.connection_key?.code,
              },
            ],
          };
        }
      } else {
        researchQuery.push({
          id: researchQuery.length > 0 ? researchQuery.length + 1 : 1,
          name: formData?.name,
          query: queryBuilderTempValue,
          updatedQuery: updatedQuery,
          source: [
            {
              type: "External",
              linked_service_id: formData?.linked_service?.id,
              linked_service_code: formData?.linked_service?.code,
              connection_key_id: formData?.connection_key?.id,
              connection_key_code: formData?.connection_key?.code,
            },
          ],
        });
      }

      setResearchQueryData((prev: any) => ({
        ...prev,
        research_queries: researchQuery,
      }));

      setFormData({ name: "" });
      setTempGlobalVariables({});
      setQueryBuilderTempValue("SELECT * FROM <table_name>");
      handleTransition(false);
      setQueryType("");
      setIsEditQuery(false);
      setIsAddQueryBtnEnabled(true);

      if (handleSaveResearchQuery) {
        handleSaveResearchQuery(researchQuery);
      }
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      if (validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            const fieldName = String(error.path).replace(/^source?\./, "");
            newErrors[fieldName] = error.message;
          }
        );
      }
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  useEffect(() => {
    if (isEditQuery) {
      setFormData(editFormData);
    }
  }, [editFormData]);

  useEffect(() => {
    if (
      formData?.linked_service?.connection_details?.connection_keys &&
      fetchedConnectionKeys
    ) {
      const validKeys = fetchedConnectionKeys.filter((key: any) =>
        formData.linked_service.connection_details.connection_keys.includes(
          key.id
        )
      );
      setConnectionKeys(validKeys);
    } else {
      setConnectionKeys([]);
    }
  }, [formData?.linked_service, fetchedConnectionKeys]);
  useEffect(() => {
    const linked =
      linkedServicesData?.find(
        (s: any) => s.id === formData?.linked_service?.id
      ) ||
      linkedServicesData?.find(
        (s: any) => s.id === formData?.source?.linked_service_id
      );
    setLinkedServicesValue(linked || null);
  }, [formData?.linked_service?.id, formData?.source?.linked_service_id]);
  useEffect(() => {
    const keyMatch =
      fetchedConnectionKeys?.find((option: { id: any }) => {
        return option.id === formData?.connection_key?.id;
      }) ??
      fetchedConnectionKeys?.find(
        (option: { id: any }) =>
          option.id === formData?.source?.connection_key_id
      );

    setConnectionKeysValue(keyMatch || null);
  }, [formData?.connection_key?.id, formData?.source?.connection_key_id]);

  const handleLinkedServiceChange = (event: any, value: any, reason: any) => {
    if (reason === "clear") {
      setFormData((prev: any) => ({
        ...prev,
        ["linked_service"]: null,
        source: {
          ...prev.source,
          linked_service_id: null,
        },
      }));
    }
    setFormData((prev: any) => ({
      ...prev,
      ["linked_service"]: value,
      ["connection_key"]: null,
      source: {
        ...prev.source,
        connection_key_id: null,
      },
    }));
    validateField("linked_service", value);
  };

  const handleFieldChange = async (
    field: string,
    value: any
  ): Promise<void> => {
    // Update local form state
    const updatedFormData = formData;
    if (field === "connection_key") {
      updatedFormData[field] = value;
      updatedFormData["source"] = {
        connection_key_id: value?.id,
      };
    } else {
      updatedFormData[field] = value;
    }

    setFormData(updatedFormData);

    const keyMatch = fetchedConnectionKeys?.find(
      (k: any) => k.id === value?.id
    );
    setConnectionKeysValue(keyMatch || null);
    validateField("connection_key", value);
  };

  return (
    <>
      <Grid item xl={3} lg={3} md={3} sm>
        <TextField
          name="name"
          label={
            <span>
              Query Name
              <span className="required-asterisk">*</span>
            </span>
          }
          type="text"
          fullWidth
          variant="outlined"
          // className="form-control-autocomplete"
          value={formData?.name}
          onChange={handleChangeQuery}
          error={!!queryError?.name}
          helperText={queryError?.name || ""}
          className={`form-control-autocomplete
                            ${queryError?.name ? "has-error" : ""}
                          `}
          disabled={isFromExecution}
        />
      </Grid>
      <Grid item xl={3} lg={3} md={3} sm>
        <Autocomplete
          fullWidth
          options={linkedServices}
          getOptionLabel={(option: any) => option.name || ""}
          value={linkedServicesValue}
          renderInput={(params) => (
            <TextField
              label={
                <span>
                  Linked Service
                  <span className="required-asterisk">*</span>
                </span>
              }
              name="linked_service"
              style={{ color: "#000000" }}
              {...params}
              placeholder="Select..."
              InputLabelProps={{
                shrink: true,
              }}
              error={!!queryError?.linked_service}
              helperText={queryError?.linked_service || ""}
            />
          )}
          renderOption={(params: any, item: any) => (
            <li
              {...params}
              key={item.key}
              style={{ paddingTop: "2px", paddingBottom: "2px" }}
            >
              <ListItemText>{item.name}</ListItemText>
            </li>
          )}
          loadingText="Loading..."
          onChange={(event, value, reason) =>
            handleLinkedServiceChange(event, value, reason)
          }
          // className={`form-control-autocomplete`}
          className={`form-control-autocomplete
                          ${queryError?.linked_service ? "has-error" : ""}
                        `}
          disabled={isFromExecution}
        />
      </Grid>

      <Grid item xl={3} lg={3} md={3} sm>
        <Autocomplete
          fullWidth
          options={connectionKeys}
          getOptionLabel={(connectionData: any) => connectionData.name}
          onChange={(e, value) => handleFieldChange("connection_key", value)}
          value={connectionKeysValue}
          renderInput={(params) => (
            <TextField
              name="connection_key"
              style={{ color: "#000000" }}
              {...params}
              label={
                <span>
                  Connection Key
                  <span className="required-asterisk">*</span>
                </span>
              }
              placeholder="Select..."
              InputLabelProps={{
                shrink: true,
              }}
              error={!!queryError?.connection_key}
              helperText={queryError?.connection_key}
            />
          )}
          loadingText="Loading..."
          // className={`form-control-autocomplete`}
          className={`form-control-autocomplete ${
            queryError?.connection_key ? "has-error" : ""
          }`}
          disabled={isFromExecution}
        />
      </Grid>

      {linkedServicesValue &&
        Object.keys(linkedServicesValue).length > 0 &&
        connectionKeysValue &&
        Object.keys(connectionKeysValue).length > 0 && (
          <GenericQueryBuilderDialog
            columns={columns}
            setColumns={setColumns}
            index={15}
            openQueryDialogIndex={openQueryDialogIndex}
            setOpenQueryDialogIndex={setOpenQueryDialogIndex}
            isTextBoxRequied={true}
            queryBuilderType={"external_query"}
            mergeQueryType={`External`}
            externalCommonValues={{
              linkedServices,
              linkedServicesValue,
              connectionKeys,
              connectionKeysValue,
              queryError,
              handleFieldChange,
              handleLinkedServiceChange,
              fetchFrom: "BaseExternalBuilder",
            }}
          />
        )}

      <Grid
        item
        xs
        sx={{
          display: "flex",
          columnGap: "8px",
          textAlign: "right",
          justifyContent: "flex-end",
          alignItems: "flex-end",
        }}
      >
        <Button
          variant="contained"
          color="secondary"
          className="btn-orange btn-dark"
          onClick={() => {
            setFormData({
              name: "",
            });
            setQueryBuilderTempValue("SELECT * FROM <table_name>");
            setQueryType("");
            setEditFormData({
              name: "",
            });
            handleTransition(false);
            setIsEditQuery(false);
            setIsAddQueryBtnEnabled(true);
          }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          color="secondary"
          className="btn-orange"
          onClick={handleSaveQuery}
        >
          <SaveOutlinedIcon /> Save
        </Button>
      </Grid>
      {/* </Grid>
        </Box>
      </Box> */}
    </>
  );
};

export default GenericExternalQueryTab;
