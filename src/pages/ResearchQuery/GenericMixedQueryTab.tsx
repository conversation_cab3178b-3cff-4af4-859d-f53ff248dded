import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  FormControlLabel,
  FormGroup,
  Grid,
  ListItemText,
  MenuItem,
  TextField,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  IconCrossCircleIconSvg,
  IconMinusWhite,
  IconPlusWhite,
} from "../../common/utils/icons";
import { genericSubQueriesType } from "../../services/constants";
import { useRuleResourceContext } from "../../contexts/RuleResourceContext";
import { useToast } from "../../services/utils";
import SaveOutlinedIcon from "@mui/icons-material/SaveOutlined";
import { IResearchQuery } from "../../types/researchQuery";
import { Chip } from "@mui/material";
import { CheckCircleRounded } from "@mui/icons-material";
import * as Yup from "yup";

import { GenericResourceFormSchema, mergeQuerySchema } from "../../schemas";
import {
  getResourceColumnsByMultipleIds,
  getResourceColumnsDetail,
} from "../../services/resourcesService";
import GenericQueryBuilderDialog from "./GenericQueryBuilderDialog";
import useFetchResourceColumnsByMultipleIds from "../../hooks/useFetchResourceColumnsByMultipleIds";

interface GenericMixedQueryTabProps {
  resourcesData: any;
  columns: any;
  setColumns: any;
  isEditQuery: any;
  setIsEditQuery: any;
  researchQueryData: any;
  setResearchQueryData: any;
  handleTransition: any;
  setQueryType: any;
  editFormData: any;
  setIsAddQueryBtnEnabled: any;
  setEditFormData: any;
  handleSaveResearchQuery?: (researchQuery: IResearchQuery[]) => void;
  isFromExecution?: boolean;
  setErrors?: any;
}

const MAX_SECTIONS = process.env.REACT_APP_RESEARCH_QUERY_MAXIMUM_SUB_QUERY
  ? parseInt(process.env.REACT_APP_RESEARCH_QUERY_MAXIMUM_SUB_QUERY)
  : 3;

const GenericMixedQueryTab = ({
  resourcesData,
  columns,
  setColumns,
  isEditQuery,
  setIsEditQuery,
  researchQueryData,
  setResearchQueryData,
  handleTransition,
  setQueryType,
  handleSaveResearchQuery,
  editFormData,
  setEditFormData,
  setIsAddQueryBtnEnabled,
  isFromExecution = false,
  setErrors,
}: GenericMixedQueryTabProps) => {
  //context
  const {
    linkedServicesData,
    fetchedConnectionKeys,
    setIsLoading,
    queryBuilderTempValue,
    setQueryBuilderTempValue,
    tempGlobalVariables,
    setTempGlobalVariables,
    globalVariables,
    setGlobalVariables,
  } = useRuleResourceContext();
  const { showToast } = useToast();

  //states
  const [queryError, setQueryError] = useState<any>({});
  const [resourceColIds, setResourceColIds] = useState<any>(0);
  const [formattedColumns, setFormattedColumns] = useState<any>([]);
  const [selectedColumnNames, setSelectedColumnNames] = useState<string[]>([]);
  const [isEditing, setIsEditing] = useState(true);

  const [openQueryDialogIndex, setOpenQueryDialogIndex] = useState<
    number | null
  >(null);

  const [formData, setFormData] = useState<any>({
    query: "SELECT * FROM",
  });

  const [mixedFormData, setMixedFormData] = useState<any>([
    {
      id: 1,
      type: "",
      sub_type: "",
      resource_id: null,
      linked_service_id: null,
      connection_key_id: null,
      table_name: "",
      external_source_name: "",
      connection_details: [],
      data_pull_query: "SELECT * FROM <>",
      dependency_list: [],
      dependency_marker: "Sub Query 1",
      sub_query_name: "Sub Query 1",
    },
  ]);

  const generateValidationPayload = (data: any[]) => {
    return data.map(
      ({
        connection_key_id,
        linked_service_id,
        table_name,
        external_source_name,
      }) => ({
        connection_key_id,
        linked_service_id,
        table_name,
        external_source_name,
      })
    );
  };
  useEffect(() => {
    const fetchResourceColumns = async () => {
      const resourceIds = editFormData.source
        .map((item: any) => item.resource_id)
        .filter(Boolean);
      const matchedResources = resourcesData.filter((resource: any) =>
        resourceIds.includes(resource.id)
      );
      const matchedResourceColumnData = matchedResources
        .map((resource: any) => {
          const columnId =
            resource?.additional_properties?.resource_column_details_id;
          if (!columnId) return null;
          return {
            resourceColumnId: columnId,
            name: resource?.resource_name,
            resourceId: resource?.id,
          };
        })
        .filter(Boolean);
      const matchedResourceColumnIds = matchedResourceColumnData.map(
        (item: any) => item.resourceColumnId
      );
      const result = await getResourceColumnsByMultipleIds(
        matchedResourceColumnIds
      );
      const resultMap = new Map(
        result.map((item: any) => [
          item?.id,
          (item?.resource_column_properties?.resource_columns || []).map(
            (col: any) => ({
              name: col?.column_name,
              value: col?.column_name,
            })
          ),
        ])
      );
      const columnsByResourceId = matchedResourceColumnData.reduce(
        (acc: any, item: any) => {
          acc[item.resourceId] = {
            name: item.name,
            columns: resultMap.get(item.resourceColumnId) || [],
          };
          return acc;
        },
        {}
      );
      setColumns((prev: any) => ({
        ...prev,
        mixedQueryData: {
          ...(prev?.mixedQueryData || {}),
          ...columnsByResourceId,
        },
      }));
    };
    if (isEditQuery) {
      fetchResourceColumns();
      setFormData((prev: any) => ({
        query: editFormData?.query,
        name: editFormData?.name,
        id: editFormData?.id,
      }));
      setMixedFormData((prev: any) => {
        return editFormData.source.map((item: any, index: number) => ({
          ...item,
          id: index + 1,
        }));
      });
    }
  }, [editFormData, resourcesData]);

  // //validation function
  // const validateField = async (index: number, fieldName: any, value: any) => {
  //   try {
  //     // Debugging: Ensure everything is functioning

  //     // Copy the current item and update the specific field
  //     const updatedItem = {
  //       ...mixedFormData[index],
  //       [fieldName]: value,
  //     };

  //     // Update the full dataset
  //     const updatedData = [...mixedFormData];
  //     updatedData[index] = updatedItem;

  //     // Prepare final payload for validation
  //     const payload = generateValidationPayload(updatedData);

  //     // Validate the specific field within the array context
  //     await externalQueryFetchColumnsSchema.validateAt(
  //       `[${index}].${fieldName}`,
  //       payload
  //     );

  //     // Clear error for this field
  //     setQueryError((prevErrors: any) => ({
  //       ...prevErrors,
  //       [index]: {
  //         ...prevErrors?.[index],
  //         [fieldName]: undefined,
  //       },
  //     }));
  //   } catch (validationError: any) {
  //     setQueryError((prevErrors: any) => ({
  //       ...prevErrors,
  //       [index]: {
  //         ...prevErrors?.[index],
  //         [fieldName]: validationError.message,
  //       },
  //     }));
  //   }
  // };

  const validateField = async (name: any, value: any) => {
    try {
      // Create a partial form data object with only the field being changed
      const partialFormData = { ...formData, [name]: value };
      await mergeQuerySchema.validateAt(name, partialFormData);
      // If validation passes, clear any previous errors for this field
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        [name]: undefined,
      }));
    } catch (validationError: any) {
      // If validation fails, set the error message for this field
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        [name]: validationError.message,
      }));
    }
  };

  //handle functions
  const handleChangeQuery = (event: any) => {
    const { name, value } = event.target;
    setFormData((prev: any) => ({
      ...prev,
      [name]: value,
    }));
    validateField(name, value);
  };

  const handleTypeChange = (index: any, value: any) => {
    const updated = [...mixedFormData];
    updated[index].type = value;
    updated[index].sub_type = value;
    setMixedFormData(updated);
    if (columns && value === "Merged_Data") {
      setColumns((prev: any) => ({
        ...prev,
        resourceColumns: [],
        mergedColumns: columns?.missingColumns,
      }));
    } else if (columns && value === "External") {
      setColumns((prev: any) => ({
        ...prev,
        resourceColumns: [],
        mergedColumns: [],
      }));
    }
  };

  const handleFieldChange = async (
    index: number,
    field: string,
    value: any
  ): Promise<void> => {
    // Update local form state
    const updatedFormData = [...mixedFormData];
    if (field === "resource_id") {
      updatedFormData[index][field] = value?.id;
      updatedFormData[index]["resource_code"] = value?.code;
      if (updatedFormData[index]?.sub_type === "Resource") {
        updatedFormData[index]["data_pull_query"] = `SELECT * FROM ${
          value?.code ? `<${value.code}>` : ""
        }`;
      }
    } else if (field === "connection_key_id") {
      updatedFormData[index][field] = value?.id;
      updatedFormData[index]["connection_key_code"] = value?.code;
    } else if (field === "dependency_list") {
      updatedFormData[index][field] = value || [];
    } else {
      updatedFormData[index][field] = value;
    }

    setMixedFormData(updatedFormData);

    // Handle special case for resource_id
    if (field !== "resource_id") return;

    const resourceDetailId =
      value?.additional_properties?.resource_column_details_id;

    if (!resourceDetailId) return;

    try {
      setIsLoading(true);

      const result = await getResourceColumnsDetail({
        resourceColumnDetailId: resourceDetailId,
      });

      const rsColumns =
        result?.resource_column_properties?.resource_columns?.map(
          (col: any) => ({
            name: col?.column_name,
            value: col?.column_name,
          })
        ) || [];
      setColumns((prev: any) => {
        const resourceId = value?.id;
        const existingData = prev?.mixedQueryData || {};

        return {
          ...prev,
          mixedQueryData: {
            ...existingData,
            [resourceId]: {
              name: value?.code,
              columns: rsColumns,
            },
          },
        };
      });
    } catch (err) {
      console.error("Failed to fetch resource column details:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLinkedServiceChange = (index: any, field: any, value: any) => {
    const valueId = value?.id;
    const updated: any = [...mixedFormData];
    updated[index][field] = valueId;
    updated[index]["connection_details"] =
      value?.connection_details?.connection_keys;
    updated[index]["linked_service_code"] = value?.code;
    updated[index]["connection_key_id"] = null;
    setMixedFormData(updated);
    //validateField(index, field, valueId);
  };

  const addSection = () => {
    if (mixedFormData.length < MAX_SECTIONS) {
      const newLength = mixedFormData.length + 1;
      const dependencies = Array.from(
        { length: newLength - 1 },
        (_, i) => `Sub Query ${i + 1}`
      );
      setMixedFormData([
        ...mixedFormData,
        {
          id: newLength,
          type: "",
          resource_id: null,
          linked_service_id: null,
          connection_key_id: null,
          table_name: "",
          connection_details: [],
          data_pull_query: "SELECT * FROM <>",
          dependency_list: [],
          dependency_marker: `Sub Query ${newLength}`,
          sub_query_name: `Sub Query ${newLength}`,
          available_dependencies: dependencies,
        },
      ]);
    }
  };

  const [showExternalColumns, setShowExternalColumns] = useState(false);

  const handleFetchColumns = async (index: any, itemId: number) => {
    setOpenQueryDialogIndex(index);
  };

  useEffect(() => {
    const resourceItems = mixedFormData.filter(
      (item: any) => item?.type === "Resource"
    );

    const resourceIds = resourceItems
      .filter((item: any) => item?.resource_id)
      .map((item: any) => item.resource_id);

    // Check if any resource type entries exist
    if (resourceItems.length === 0) return;

    const matchedResourceIds = resourcesData
      ?.filter((resource: any) => resourceIds.includes(resource.id))
      .map(
        (resource: any) =>
          resource?.additional_properties?.resource_column_details_id
      )
      .filter(Boolean); // remove null/undefined

    const resourceCode = resourcesData?.find(
      (option: { id: any }) => option.id === formData?.source?.resource_id
    )?.code;

    if (matchedResourceIds.length > 0) {
      setResourceColIds(matchedResourceIds);
      setQueryBuilderTempValue(
        `SELECT * FROM <${resourceCode ? resourceCode : ""}> `
      );
    }
  }, [
    // Only trigger when the 'Resource'-type items in mixedFormData change
    JSON.stringify(
      mixedFormData &&
        mixedFormData.lenght > 0 &&
        mixedFormData.filter((item: any) => {
          return item.type === "Resource";
        })
    ),
    resourcesData,
    formData?.source?.resource_id,
  ]);

  const handleCloseDialog = () => {
    setShowExternalColumns(false);
  };

  // Function to handle checkbox changes

  const handleSaveDialog = (event: any) => {
    // const selectedFormattedCols = formattedColumns.filter((col: any) =>
    //   selectedColumnNames.includes(col.value)
    // );

    // setColumns((prev: any) => {
    //   // Create a new list with the unique columns (ensuring uniqueness by column value)
    //   const updatedColumns = [
    //     ...prev.externalResourceColumns,
    //     ...selectedFormattedCols,
    //   ];

    //   // Remove duplicate columns based on their value
    //   const uniqueColumns = Array.from(
    //     new Map(updatedColumns.map((col: any) => [col.value, col])).values()
    //   );

    //   return {
    //     ...prev,
    //     externalResourceColumns: uniqueColumns, // Set the unique columns
    //   };
    // });

    handleCloseDialog();
    setIsEditing(false);

    showToast("Columns saved successfully", "success");
  };

  const handleSaveQuery = async () => {
    try {
      const researchQuery: any = [...researchQueryData?.research_queries];

      try {
        await GenericResourceFormSchema.validate(researchQueryData, {
          abortEarly: false,
        });
      } catch (formErrors: any) {
        const newErrors: { [key: string]: string } = {};
        if (formErrors.inner) {
          formErrors.inner.forEach(
            (error: { path: string | number; message: string }) => {
              // const fieldName = String(error.path).replace(/^source?\./, "");
              newErrors[error.path] = error.message;
            }
          );
        }
        setErrors((prevErrors: any) => ({
          ...prevErrors,
          ...newErrors,
        }));
        return;
      }

      const updatedFormData = {
        name: formData?.name,
        query: formData.query,
      };

      await mergeQuerySchema.validate(updatedFormData, {
        abortEarly: false,
      });
      // Variable validations
      const variableSchema: any = {};
      Object.keys(tempGlobalVariables).forEach((key: any) => {
        if (tempGlobalVariables[key] === "") {
          variableSchema[key] = Yup.string().required(`Please enter ${key}`);
          setQueryError((prevErrors: any) => ({
            ...prevErrors,
            [key]: `Please enter ${key}`,
          }));
        }
      });
      const resourceQueryVariableSchema = Yup.object().shape(variableSchema);
      await resourceQueryVariableSchema.validate(tempGlobalVariables, {
        abortEarly: false,
      });
      setGlobalVariables((prev: any) => ({
        ...Object.keys(tempGlobalVariables).reduce(
          (acc, key) => ({ ...acc, [key]: tempGlobalVariables[key] }),
          prev
        ),
      }));
      // const updatedQuery: string = replaceQueryToDomainMapped(
      //   queryBuilderTempValue,
      //   columns?.missingColumns
      // );
      let multipleSource: any[] = [];

      for (const item of mixedFormData) {
        if (
          item?.sub_type === "External" &&
          (!item?.external_source_name ||
            item.external_source_name.trim() === "")
        ) {
          showToast(
            `External source name is required for External sub_type, Please click on 'Configure Query' button`,
            "error"
          );

          return;
        }

        multipleSource.push({
          ...item,
          type: "Mixed",
          sub_type: item?.sub_type,
          resource_id: item?.resource_id,
          resource_code: item?.resource_code,
          linked_service_id: item?.linked_service_id,
          linked_service_code: item?.linked_service_code,
          connection_key_id: item?.connection_key_id,
          connection_key_code: item?.connection_key_code,
          external_source_name: item?.external_source_name,
          data_pull_query: item?.data_pull_query,
        });
      }
      if (isEditQuery) {
        const index = researchQuery.findIndex(
          (item: any) => item.id === formData.id
        );

        if (index !== -1) {
          researchQuery[index] = {
            id: formData.id,
            name: formData.name,
            query: formData.query,
            //updatedQuery: updatedQuery,
            source: multipleSource,
          };
        }
      } else {
        researchQuery.push({
          id: researchQuery.length > 0 ? researchQuery.length + 1 : 1,
          name: formData?.name,
          query: formData.query,
          //updatedQuery: updatedQuery,
          source: multipleSource,
        });
      }
      setResearchQueryData((prev: any) => ({
        ...prev,
        research_queries: researchQuery,
      }));
      setFormData({ name: "" });
      setTempGlobalVariables({});
      setQueryBuilderTempValue("SELECT * FROM <>");
      handleTransition(false);
      setQueryType("");
      setIsEditQuery(false);
      setIsAddQueryBtnEnabled(true);
      if (handleSaveResearchQuery) {
        handleSaveResearchQuery(researchQuery);
      }
    } catch (validationErrors: any) {
      const newErrors: { [key: string]: string } = {};
      if (validationErrors.inner) {
        validationErrors.inner.forEach(
          (error: { path: string | number; message: string }) => {
            const fieldName = String(error.path).replace(/^source?\./, "");
            newErrors[fieldName] = error.message;
          }
        );
      }
      setQueryError((prevErrors: any) => ({
        ...prevErrors,
        ...newErrors,
      }));
    }
  };

  const handleRemoveSubQuery = (index: number) => {
    const updatedFormData = [...mixedFormData];
    updatedFormData.splice(index, 1);
    setMixedFormData(updatedFormData);
  };

  return (
    <>
      <Grid item xl={3} lg={3} md={3} sm>
        <TextField
          name="name"
          label={
            <span>
              Query Name
              <span className="required-asterisk">*</span>
            </span>
          }
          type="text"
          fullWidth
          variant="outlined"
          // className="form-control-autocomplete"
          value={formData?.name}
          onChange={handleChangeQuery}
          error={!!queryError?.name}
          helperText={queryError?.name || ""}
          className={`form-control-autocomplete ${
            queryError?.name ? "has-error" : ""
          }`}
          disabled={isFromExecution}
        />
      </Grid>

      <Grid item sm={12} md={12} lg={12} xl={12}>
        {mixedFormData.length > 0 &&
          mixedFormData.map((item: any, index: any) => (
            <Box className="query-sub-type">
              <Grid
                container
                columnSpacing={1.5}
                rowSpacing={2.5}
                key={item.id}
              >
                <Grid item xl={3} lg={3} md={3}>
                  <Autocomplete
                    fullWidth
                    options={genericSubQueriesType ?? []}
                    getOptionLabel={(option: any) =>
                      option ? `${option.replace("_", " ")} Queries` : ""
                    }
                    value={
                      genericSubQueriesType?.find(
                        (option) => option == item?.sub_type
                      ) || null
                    }
                    renderInput={(params) => (
                      <TextField
                        label={<span>Select Sub Query {index + 1}</span>}
                        name="queryType"
                        style={{ color: "#000000" }}
                        {...params}
                        placeholder="Select..."
                        InputLabelProps={{
                          shrink: true,
                        }}
                      />
                    )}
                    renderOption={(params: any, item: any) => (
                      <li
                        {...params}
                        key={item.key}
                        style={{
                          paddingTop: "2px",
                          paddingBottom: "2px",
                        }}
                      >
                        <ListItemText>
                          <span
                            style={{ textTransform: "capitalize" }}
                          >{`${item.replace("_", " ")} Queries`}</span>
                        </ListItemText>
                      </li>
                    )}
                    loadingText="Loading..."
                    onChange={(e, value) => handleTypeChange(index, value)}
                    className={`form-control-autocomplete`}
                    disabled={isFromExecution}
                  />
                </Grid>

                {item.sub_type === "Resource" && (
                  <>
                    <Grid item xl={3} lg={3} md={3} sm>
                      <TextField
                        label={<span>Sub Query Name</span>}
                        type="text"
                        fullWidth
                        variant="outlined"
                        className={`form-control-autocomplete`}
                        name="sub_query_name"
                        value={
                          item?.sub_query_name || `Sub Query  ${index + 1}`
                        }
                        onChange={(e) =>
                          handleFieldChange(
                            index,
                            "sub_query_name",
                            e.target.value
                          )
                        }
                        disabled={isFromExecution}
                      />
                    </Grid>
                    <Grid item xl={3} lg={3} md={3} sm>
                      <Autocomplete
                        fullWidth
                        options={resourcesData ?? []}
                        getOptionLabel={(option: any) =>
                          option.resource_name || ""
                        }
                        value={
                          resourcesData?.find(
                            (option: { id: any }) =>
                              option.id == item?.resource_id
                          ) || null
                        }
                        renderInput={(params) => (
                          <TextField
                            label={<span>Select Resources</span>}
                            name="resource_id"
                            style={{ color: "#000000" }}
                            {...params}
                            placeholder="Select..."
                            InputLabelProps={{
                              shrink: true,
                            }}
                          />
                        )}
                        renderOption={(params: any, item: any) => (
                          <li
                            {...params}
                            key={item.key}
                            style={{
                              paddingTop: "2px",
                              paddingBottom: "2px",
                            }}
                          >
                            <ListItemText>{item.resource_name}</ListItemText>
                          </li>
                        )}
                        loadingText="Loading..."
                        onChange={(e, value) =>
                          handleFieldChange(index, "resource_id", value)
                        }
                        className={`form-control-autocomplete`}
                        disabled={isFromExecution}
                      />
                    </Grid>
                    <GenericQueryBuilderDialog
                      columns={columns}
                      setColumns={setColumns}
                      title={`Resource Query`}
                      mergeQueryType={`Resource`}
                      setMixedFormData={setMixedFormData}
                      index={index}
                      openQueryDialogIndex={openQueryDialogIndex}
                      setOpenQueryDialogIndex={setOpenQueryDialogIndex}
                      isFinalQuery={false}
                      queryBuilderType={"resource_query"}
                      fetchExternalColumnsData={{
                        mixedFormData,
                        setFormData,
                        setQueryError,
                        queryError,
                        handleFieldChange,
                        handleLinkedServiceChange,
                        setMixedFormData,
                        itemId: item.id,
                        formData,
                      }}
                      queryType={"mixedDependencyColumns"}
                    />
                  </>
                )}

                {item.sub_type === "External" && (
                  <>
                    {(() => {
                      const linkedServices = linkedServicesData
                        ? linkedServicesData.filter(
                            (service: any) => service?.type === "sql"
                          )
                        : [];
                      const linkedServicesValue =
                        linkedServicesData?.find(
                          (option: { id: any }) =>
                            option.id === item?.linked_service_id
                        ) ?? null;
                      const connectionKeys =
                        (fetchedConnectionKeys
                          ? fetchedConnectionKeys.filter((key: any) =>
                              item?.connection_details?.includes(key.id)
                            )
                          : []) ?? [];
                      const connectionKeysValue =
                        fetchedConnectionKeys?.find(
                          (option: { id: any }) =>
                            option.id === item?.connection_key_id
                        ) ??
                        fetchedConnectionKeys?.find(
                          (option: { id: any }) =>
                            option.id === item?.connection_key_id
                        ) ??
                        null;
                      return (
                        <>
                          <Grid item xl={3} lg={3} md={3} sm>
                            <TextField
                              label={<span>Sub Query Name</span>}
                              type="text"
                              fullWidth
                              variant="outlined"
                              className={`form-control-autocomplete`}
                              name="sub_query_name"
                              value={
                                item?.sub_query_name ||
                                `Sub Query  ${index + 1}`
                              }
                              onChange={(e) =>
                                handleFieldChange(
                                  index,
                                  "sub_query_name",
                                  e.target.value
                                )
                              }
                              disabled={isFromExecution}
                            />
                          </Grid>
                          <Grid item xl={3} lg={3} md={3} sm>
                            <Autocomplete
                              fullWidth
                              options={linkedServices ?? []}
                              getOptionLabel={(option: any) =>
                                option.name || ""
                              }
                              value={linkedServicesValue}
                              renderInput={(params) => (
                                <TextField
                                  label={<span>Linked Service</span>}
                                  name="linked_service"
                                  style={{ color: "#000000" }}
                                  {...params}
                                  placeholder="Select..."
                                  InputLabelProps={{
                                    shrink: true,
                                  }}
                                />
                              )}
                              renderOption={(params: any, item: any) => (
                                <li
                                  {...params}
                                  key={item.key}
                                  style={{
                                    paddingTop: "2px",
                                    paddingBottom: "2px",
                                  }}
                                >
                                  <ListItemText>{item.name}</ListItemText>
                                </li>
                              )}
                              loadingText="Loading..."
                              onChange={(e, value) =>
                                handleLinkedServiceChange(
                                  index,
                                  "linked_service_id",
                                  value
                                )
                              }
                              className={`form-control-autocomplete`}
                              disabled={isFromExecution}
                            />
                          </Grid>
                          <Grid item xl={3} lg={3} md={3} sm>
                            <Autocomplete
                              fullWidth
                              options={connectionKeys}
                              getOptionLabel={(connectionData: any) =>
                                connectionData.name
                              }
                              value={connectionKeysValue}
                              renderInput={(params) => (
                                <TextField
                                  name="connection_key"
                                  style={{ color: "#000000" }}
                                  {...params}
                                  label={<span>Connection Key</span>}
                                  placeholder="Select..."
                                  InputLabelProps={{
                                    shrink: true,
                                  }}
                                />
                              )}
                              loadingText="Loading..."
                              onChange={(e, value) =>
                                handleFieldChange(
                                  index,
                                  "connection_key_id",
                                  value
                                )
                              }
                              className={`form-control-autocomplete`}
                              disabled={isFromExecution}
                            />
                          </Grid>

                          {item?.connection_key_id &&
                            item?.linked_service_id && (
                              <GenericQueryBuilderDialog
                                columns={columns}
                                setColumns={setColumns}
                                title={`External Query`}
                                mergeQueryType={`External`}
                                setMixedFormData={setMixedFormData}
                                index={index}
                                openQueryDialogIndex={openQueryDialogIndex}
                                setOpenQueryDialogIndex={
                                  setOpenQueryDialogIndex
                                }
                                isFinalQuery={false}
                                queryBuilderType={"external_query"}
                                externalCommonValues={{
                                  linkedServices,
                                  linkedServicesValue,
                                  connectionKeys,
                                  connectionKeysValue,
                                  queryError,
                                  handleFieldChange,
                                  handleLinkedServiceChange,
                                  fetchFrom: "MixedExternalBuilder",
                                }}
                                fetchExternalColumnsData={{
                                  mixedFormData,
                                  setFormData,
                                  setQueryError,
                                  setMixedFormData,
                                  itemId: item.id,
                                  formData,
                                }}
                                queryType={"mixedDependencyColumns"}
                                isEditQuery={isEditQuery}
                              />
                            )}
                        </>
                      );
                    })()}
                  </>
                )}

                {index !== 0 && (
                  <Grid item xl={3} lg={3} md={3} sm>
                    <Autocomplete
                      fullWidth
                      multiple
                      options={item.available_dependencies || []}
                      getOptionLabel={(option: any) => option}
                      value={item?.dependency_list || []}
                      renderInput={(params) => (
                        <div className="autocomplete-chips-direction">
                          <TextField
                            name="dependency_list"
                            style={{ color: "#000000" }}
                            {...params}
                            label={<span>Dependencies</span>}
                            placeholder="Select..."
                            InputLabelProps={{
                              shrink: true,
                            }}
                          />
                        </div>
                      )}
                      renderTags={(value, getTagProps) => {
                        return (
                          <div className="align-from-left">
                            {value.map((option: any, index: number) => {
                              return (
                                <Chip
                                  {...getTagProps({ index })}
                                  key={index}
                                  label={option}
                                />
                              );
                            })}
                          </div>
                        );
                      }}
                      renderOption={(props, option: any, { selected }) => {
                        return (
                          <MenuItem
                            {...props}
                            key={`${option}`}
                            value={option}
                            sx={{ justifyContent: "space-between" }}
                          >
                            {option}
                            {selected ? (
                              <CheckCircleRounded color="info" />
                            ) : null}
                          </MenuItem>
                        );
                      }}
                      loadingText="Loading..."
                      onChange={(e, value) =>
                        handleFieldChange(index, "dependency_list", value)
                      }
                      className={`form-control-autocomplete`}
                      disabled={isFromExecution}
                    />
                  </Grid>
                )}
                {item.type === "External" && (
                  <Grid
                    item
                    sm
                    sx={{
                      display: "flex",
                      alignItems: "flex-end",
                      justifyContent: "flex-end",
                    }}
                  >
                    {!showExternalColumns && isEditing && (
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "flex-end",
                          columnGap: 2,
                        }}
                      >
                        <Button
                          color="secondary"
                          variant="contained"
                          onClick={() => handleFetchColumns(index, item.id)}
                          className="btn-orange btn-blue"
                          disabled={
                            !(
                              item?.connection_key_id && item?.linked_service_id
                            ) || isFromExecution
                          }
                        >
                          Configure Query
                        </Button>
                      </Box>
                    )}
                  </Grid>
                )}
              </Grid>

              <Box
                sx={{
                  position: "absolute",
                  top: "-12px",
                  right: "-12px",
                  cursor: "pointer",
                  display: isFromExecution ? "none" : "block",
                }}
                onClick={() => handleRemoveSubQuery(index)}
              >
                <IconCrossCircleIconSvg />
              </Box>
            </Box>
          ))}
      </Grid>

      {mixedFormData.length > 0 &&
        mixedFormData.filter((item: any) => item.type !== "").length >= 2 && (
          <>
            <GenericQueryBuilderDialog
              columns={columns}
              title={`Main Query`}
              mergeQueryType={`Query`}
              setMixedFormData={setMixedFormData}
              setColumns={setColumns}
              index={15} //pass any number
              openQueryDialogIndex={openQueryDialogIndex}
              setOpenQueryDialogIndex={setOpenQueryDialogIndex}
              fetchExternalColumnsData={{
                handleLinkedServiceChange,
                handleFieldChange,
                queryError,
                setQueryError,
                mixedFormData,
                setMixedFormData,
                formData,
                setFormData,
              }}
              queryType={"mixed"}
              isFinalQuery={true}
              queryBuilderType={"main_query"}
            />
          </>
        )}
      <Grid
        item
        xs
        sx={{
          display: "flex",
          columnGap: "8px",
          textAlign: "right",
          justifyContent: "flex-end",
          alignItems: "flex-end",
        }}
      >
        {mixedFormData.length < MAX_SECTIONS && (
          <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
            <Button
              color="secondary"
              variant="contained"
              onClick={addSection}
              className="btn-orange"
              disabled={isFromExecution}
            >
              + &nbsp;Add Sub-Query
            </Button>
          </Box>
        )}
        {mixedFormData.length > 0 &&
          mixedFormData.filter((item: any) => item.type !== "").length >= 2 && (
            <>
              <Button
                variant="contained"
                color="secondary"
                className="btn-orange btn-dark"
                onClick={() => {
                  setFormData({
                    name: "",
                  });
                  setQueryBuilderTempValue(`SELECT * FROM <>`);
                  setQueryType("");
                  setQueryError({});
                  setEditFormData({
                    name: "",
                  });
                  handleTransition(false);
                  setIsEditQuery(false);
                  setIsAddQueryBtnEnabled(true);
                }}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                color="secondary"
                className="btn-orange"
                onClick={handleSaveQuery}
              >
                <SaveOutlinedIcon /> Save
              </Button>
            </>
          )}
      </Grid>

      <Dialog
        fullWidth
        maxWidth="sm"
        open={showExternalColumns}
        onClose={handleCloseDialog}
        className="main-dailog"
      >
        <Box sx={{ padding: 3 }}>
          <Grid container className="dailog-header">
            <label className="label-text pt-13">Choose Items</label>
            <div className="close-icon" onClick={handleCloseDialog}></div>
          </Grid>
          <Box className="dailog-body pb-24">
            <Box>
              <FormGroup>
                {formattedColumns.map((col: any) => (
                  <FormControlLabel
                    key={col.value}
                    control={
                      <Checkbox
                        checked={selectedColumnNames.includes(col.value)}
                        onChange={(e) => {
                          const { checked } = e.target;
                          setSelectedColumnNames((prev) =>
                            checked
                              ? [...prev, col.value]
                              : prev.filter((val) => val !== col.value)
                          );
                        }}
                        name={col.value}
                        sx={{
                          "&.Mui-checked": {
                            color: "#196BB4",
                          },
                        }}
                      />
                    }
                    label={col.name}
                  />
                ))}
              </FormGroup>
            </Box>
          </Box>
          <DialogActions className="dailog-footer">
            <Button
              variant="contained"
              color="secondary"
              className="btn-orange"
              onClick={handleSaveDialog}
            >
              <SaveOutlinedIcon /> &nbsp; Carry Selected Columns
            </Button>
          </DialogActions>
        </Box>
      </Dialog>

      {/* <Dialog
        fullWidth
        maxWidth="sm"
        open={showExternalColumns}
        onClose={handleCloseDialog}
        className="main-dailog"
      >
        <Box sx={{ padding: 3 }}>
          <Grid container className="dailog-header">
            <label className="label-text pt-13">Choose Items</label>
            <div className="close-icon" onClick={handleCloseDialog}></div>
          </Grid>
          <Box className="dailog-body pb-24"></Box>
        </Box>
        <DialogActions className="dailog-footer"></DialogActions>
      </Dialog> */}
    </>
  );
};

export default GenericMixedQueryTab;
